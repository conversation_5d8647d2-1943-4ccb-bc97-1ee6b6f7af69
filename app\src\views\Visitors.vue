<template>
  <page-layout title="访客记录" :show-back="true" hide-tab-bar>
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="spinner" />
      <p>加载中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="hasError" class="error-container">
      <div class="error-content">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="48"
          height="48"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="1.5"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <circle cx="12" cy="12" r="10"/>
          <line x1="12" y1="8" x2="12" y2="12"/>
          <line x1="12" y1="16" x2="12.01" y2="16"/>
        </svg>
        <h3>加载失败</h3>
        <button class="retry-button" @click="fetchVisitors">重新加载</button>
      </div>
    </div>

    <!-- 访客列表 -->
    <div v-else class="visitors-container">
      <!-- 统计信息 -->
      <div class="visitors-stats">
        <div class="stat-item">
          <span class="stat-number">{{ totalVisitors }}</span>
          <span class="stat-label">总访客数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ totalVisits }}</span>
          <span class="stat-label">总访问次数</span>
        </div>
        <div class="stat-item">
          <span class="stat-number">{{ todayVisitors }}</span>
          <span class="stat-label">今日访客</span>
        </div>
      </div>

      <!-- 访客列表 -->
      <div v-if="visitors.length > 0" class="visitors-list">
        <div 
          v-for="visitor in visitors" 
          :key="visitor.id" 
          class="visitor-card"
          @click="viewProfile(visitor.id)"
        >
          <div class="visitor-avatar">
            <avatar :src="visitor.avatar" size="medium" />
          </div>
          
          <div class="visitor-info">
            <div class="visitor-header">
              <h3 class="visitor-name">{{ visitor.name }}</h3>
              <span class="visit-count">访问{{ visitor.visitCount }}次</span>
            </div>
            
            <div class="visitor-details">
              <span class="visitor-age">{{ visitor.age }}岁</span>
              <span class="visitor-region">{{ visitor.region || '未知地区' }}</span>
            </div>
            
            <div class="visit-time">{{ formatVisitTime(visitor.visitTime) }}</div>
          </div>

          <div class="visitor-actions">
            <button class="action-btn view-btn" @click.stop="viewProfile(visitor.id)">
              查看
            </button>
          </div>
        </div>

        <!-- 加载更多 -->
        <div v-if="hasMore" class="load-more-container">
          <button 
            v-if="!loadingMore" 
            class="load-more-btn" 
            @click="loadMore"
          >
            加载更多
          </button>
          <div v-else class="loading-more">
            <div class="spinner small" />
            <span>加载中...</span>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-visitors">
        <svg
          width="64"
          height="64"
          viewBox="0 0 24 24"
          fill="none"
          stroke="#ccc"
          stroke-width="1.5"
        >
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
          <circle cx="12" cy="7" r="4" />
        </svg>
        <h3>暂无访客记录</h3>
        <p>还没有人访问过您的资料</p>
      </div>
    </div>
  </page-layout>
</template>

<script setup>
  import { ref, onMounted, inject } from 'vue'
  import { useRouter } from 'vue-router'
  import PageLayout from '../components/PageLayout.vue'
  import Avatar from '../components/Avatar.vue'
  import { userService } from '../services/userService'

  const router = useRouter()
  const toast = inject('toast')

  // 状态管理
  const isLoading = ref(true)
  const hasError = ref(false)
  const loadingMore = ref(false)

  // 分页状态
  const currentPage = ref(1)
  const hasMore = ref(true)

  // 数据
  const visitors = ref([])
  const totalVisitors = ref(0)
  const totalVisits = ref(0)
  const todayVisitors = ref(0)

  // 获取访客列表
  const fetchVisitors = async (page = 1, append = false) => {
    try {
      if (page === 1) {
        isLoading.value = true
        hasError.value = false
      } else {
        loadingMore.value = true
      }

      const response = await userService.getVisitors({ 
        page, 
        limit: 20 
      })

      if (response && response.data) {
        const newVisitors = response.data.map(visitor => ({
          ...visitor,
          visitTime: new Date(visitor.visitTime)
        }))

        if (append) {
          visitors.value.push(...newVisitors)
        } else {
          visitors.value = newVisitors
        }

        // 更新分页信息
        hasMore.value = response.pagination && response.pagination.page < response.pagination.totalPages
        totalVisitors.value = response.pagination ? response.pagination.total : newVisitors.length

        // 在第一页时获取统计数据
        if (page === 1) {
          try {
            const stats = await userService.getUserStats()
            if (stats) {
              totalVisits.value = stats.totalVisits || 0
            }
          } catch (error) {
            console.warn('获取统计数据失败:', error)
          }
        }

        // 计算今日访客数
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        todayVisitors.value = visitors.value.filter(visitor => {
          const visitDate = new Date(visitor.visitTime)
          visitDate.setHours(0, 0, 0, 0)
          return visitDate.getTime() === today.getTime()
        }).length
      }

    } catch (error) {
      console.error('获取访客列表失败:', error)
      if (page === 1) {
        hasError.value = true
      } else {
        toast.show('加载更多失败', 'error')
      }
    } finally {
      isLoading.value = false
      loadingMore.value = false
    }
  }

  // 加载更多
  const loadMore = () => {
    if (!hasMore.value || loadingMore.value) return
    
    currentPage.value++
    fetchVisitors(currentPage.value, true)
  }

  // 查看用户资料
  const viewProfile = (userId) => {
    router.push(`/profile/${userId}`)
  }



  // 格式化访问时间
  const formatVisitTime = (visitTime) => {
    const now = new Date()
    const diff = now - visitTime
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 60) {
      return `${minutes}分钟前`
    } else if (hours < 24) {
      return `${hours}小时前`
    } else if (days < 30) {
      return `${days}天前`
    } else {
      return visitTime.toLocaleDateString()
    }
  }

  // 组件挂载
  onMounted(() => {
    fetchVisitors()
  })
</script>

<style scoped>
  .visitors-container {
    padding: 15px;
  }

  /* 统计信息 */
  .visitors-stats {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding: 20px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .stat-item {
    flex: 1;
    text-align: center;
  }

  .stat-number {
    display: block;
    font-size: 24px;
    font-weight: 600;
    color: #ff5864;
    margin-bottom: 5px;
  }

  .stat-label {
    font-size: 14px;
    color: #666;
  }

  /* 访客卡片 */
  .visitors-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .visitor-card {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .visitor-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .visitor-avatar {
    margin-right: 15px;
  }

  .visitor-info {
    flex: 1;
  }

  .visitor-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 5px;
  }

  .visitor-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
  }

  .visit-count {
    font-size: 12px;
    color: #ff5864;
    background-color: rgba(255, 88, 100, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
  }

  .visitor-details {
    display: flex;
    gap: 15px;
    margin-bottom: 5px;
  }

  .visitor-age,
  .visitor-region {
    font-size: 14px;
    color: #666;
  }

  .visit-time {
    font-size: 12px;
    color: #999;
  }

  .visitor-actions {
    margin-left: 15px;
  }

  .action-btn {
    padding: 8px 16px;
    border-radius: 20px;
    border: none;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .view-btn {
    background-color: #ff5864;
    color: white;
  }

  .view-btn:hover {
    background-color: #e54d5a;
  }

  /* 加载更多 */
  .load-more-container {
    padding: 20px 0;
    text-align: center;
  }

  .load-more-btn {
    padding: 12px 24px;
    background-color: #f8f9fa;
    color: #666;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .load-more-btn:hover {
    background-color: #e9ecef;
  }

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: #666;
  }

  /* 空状态 */
  .empty-visitors {
    text-align: center;
    padding: 60px 20px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .empty-visitors h3 {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin: 20px 0 10px 0;
  }

  .empty-visitors p {
    font-size: 14px;
    color: #999;
    margin: 0;
  }

  /* 加载状态 */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
  }

  .loading-container p {
    margin-top: 15px;
    color: #666;
  }

  /* 错误状态 */
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60px 20px;
  }

  .error-content {
    text-align: center;
    background-color: #fff;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .error-content svg {
    color: #ff6b6b;
    margin-bottom: 20px;
  }

  .error-content h3 {
    font-size: 18px;
    font-weight: 500;
    margin: 0 0 20px 0;
    color: #333;
  }

  .retry-button {
    background-color: #ff5864;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .retry-button:hover {
    background-color: #e54d5a;
  }

  /* 加载器 */
  .spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(255, 88, 100, 0.2);
    border-top-color: #ff5864;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .spinner.small {
    width: 20px;
    height: 20px;
    border-width: 2px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* 响应式设计 */
  @media (max-width: 375px) {
    .visitors-container {
      padding: 10px;
    }

    .visitor-card {
      padding: 12px;
    }

    .visitors-stats {
      gap: 15px;
      padding: 15px;
    }
  }
</style>
