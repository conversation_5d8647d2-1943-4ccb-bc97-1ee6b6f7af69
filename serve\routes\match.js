const express = require('express')
const { readData, addData, updateData, findData, paginateData } = require('../utils/dataStore')
const RegionUtils = require('../utils/regionUtils')

const router = express.Router()

/**
 * 获取推荐用户列表
 * GET /api/matches/recommendations
 */
router.get('/recommendations', async (req, res) => {
  try {
    const { gender, age_min, age_max, provinceCode, cityCode, tags } = req.query
    const currentUserId = req.user.id

    // 获取所有用户
    const allUsers = await readData('users')
    
    // 获取当前用户已经操作过的用户（喜欢/不喜欢）
    const likes = await readData('likes')
    const userActions = likes.filter(like => like.userId == currentUserId)
    const actionedUserIds = userActions.map(action => action.targetUserId)

    // 过滤用户
    let filteredUsers = allUsers.filter(user => {
      // 排除自己和已操作过的用户
      if (user.id == currentUserId || actionedUserIds.includes(user.id)) {
        return false
      }

      // 只显示已完善资料的用户
      if (!user.profileCompleted) {
        return false
      }

      // 性别筛选
      if (gender && user.gender !== gender) {
        return false
      }

      // 年龄筛选
      if (age_min && user.age < parseInt(age_min)) {
        return false
      }
      if (age_max && user.age > parseInt(age_max)) {
        return false
      }

      // 地区筛选
      if (provinceCode && user.provinceCode !== provinceCode) {
        return false
      }
      if (cityCode && user.cityCode !== cityCode) {
        return false
      }

      // 标签筛选
      if (tags) {
        const searchTags = Array.isArray(tags) ? tags : [tags]
        const userTags = user.tags || []
        if (!searchTags.some(tag => userTags.includes(tag))) {
          return false
        }
      }

      return true
    })

    // 随机打乱推荐顺序
    filteredUsers = filteredUsers.sort(() => Math.random() - 0.5)

    // 只返回必要的字段
    const recommendations = filteredUsers.map(user => ({
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      age: user.age,
      gender: user.gender,
      provinceCode: user.provinceCode || '',
      cityCode: user.cityCode || '',
      region: RegionUtils.formatRegionString(user.provinceCode, user.cityCode) || '',
      tags: user.tags,
      avatar: user.avatar, // 头像字段
      image: user.avatar,  // 兼容旧版本
      images: user.images || [user.avatar] // 相册图片
    }))

    res.json({
      code: 0,
      data: recommendations.slice(0, 20) // 最多返回20个推荐
    })

  } catch (error) {
    console.error('获取推荐用户失败:', error)
    res.status(500).json({
      code: 500,
      message: '获取推荐用户失败'
    })
  }
})

/**
 * 匹配操作（喜欢/不喜欢）
 * POST /api/matches/action
 */
router.post('/action', async (req, res) => {
  try {
    const { targetUserId, action } = req.body
    const currentUserId = req.user.id

    // 参数验证
    if (!targetUserId || !action) {
      return res.status(400).json({
        code: 2001,
        message: '缺少必要参数'
      })
    }

    if (!['like', 'dislike'].includes(action)) {
      return res.status(400).json({
        code: 2001,
        message: '无效的操作类型'
      })
    }

    // 检查目标用户是否存在
    const users = await readData('users')
    const targetUser = users.find(u => u.id == targetUserId)
    
    if (!targetUser) {
      return res.status(404).json({
        code: 4001,
        message: '目标用户不存在'
      })
    }

    // 检查是否已经操作过
    const likes = await readData('likes')
    const existingAction = likes.find(like => 
      like.userId == currentUserId && like.targetUserId == targetUserId
    )

    if (existingAction) {
      return res.status(400).json({
        code: 2001,
        message: '已经操作过该用户'
      })
    }

    // 记录操作
    const likeRecord = await addData('likes', {
      userId: currentUserId,
      targetUserId: parseInt(targetUserId),
      action,
      likeTime: new Date().toISOString()
    })

    let matched = false
    let matchId = null

    // 如果是喜欢操作，检查是否互相喜欢
    if (action === 'like') {
      const mutualLike = likes.find(like => 
        like.userId == targetUserId && 
        like.targetUserId == currentUserId && 
        like.action === 'like'
      )

      if (mutualLike) {
        // 创建匹配记录
        const match = await addData('matches', {
          user1Id: Math.min(currentUserId, targetUserId),
          user2Id: Math.max(currentUserId, targetUserId),
          matchTime: new Date().toISOString(),
          lastActiveTime: new Date().toISOString()
        })

        matched = true
        matchId = match.id

        // 通知对方匹配成功（如果在线）
        if (req.app.broadcastToUser) {
          req.app.broadcastToUser(targetUserId, {
            type: 'match',
            message: '你们匹配成功了！',
            matchId: match.id,
            user: {
              id: req.user.id,
              username: req.user.username,
              nickname: req.user.nickname,
              avatar: req.user.avatar
            }
          })
        }
      }
    }

    res.json({
      code: 0,
      message: '操作成功',
      data: {
        matched,
        matchId
      }
    })

  } catch (error) {
    console.error('匹配操作失败:', error)
    res.status(500).json({
      code: 500,
      message: '操作失败'
    })
  }
})

/**
 * 获取匹配列表
 * GET /api/matches
 */
router.get('/', async (req, res) => {
  try {
    const currentUserId = req.user.id

    // 获取匹配记录
    const matches = await readData('matches')
    const userMatches = matches.filter(match => 
      match.user1Id == currentUserId || match.user2Id == currentUserId
    )

    // 获取用户信息
    const users = await readData('users')
    
    const matchList = userMatches.map(match => {
      const partnerId = match.user1Id == currentUserId ? match.user2Id : match.user1Id
      const partner = users.find(u => u.id == partnerId)
      
      return {
        id: match.id,
        userId: partnerId,
        username: partner?.username || '未知用户',
        nickname: partner?.nickname,
        avatar: partner?.avatar || '/uploads/default-avatar.png',
        matchTime: match.matchTime,
        lastActiveTime: match.lastActiveTime
      }
    })

    // 按匹配时间倒序排列
    matchList.sort((a, b) => new Date(b.matchTime) - new Date(a.matchTime))

    res.json({
      code: 0,
      data: matchList
    })

  } catch (error) {
    console.error('获取匹配列表失败:', error)
    res.status(500).json({
      code: 500,
      message: '获取匹配列表失败'
    })
  }
})

/**
 * 获取喜欢我的用户列表
 * GET /api/likes/received
 */
router.get('/received', async (req, res) => {
  try {
    const currentUserId = req.user.id

    // 获取喜欢我的记录
    const likes = await readData('likes')
    const receivedLikes = likes.filter(like => 
      like.targetUserId == currentUserId && like.action === 'like'
    )

    // 获取用户信息
    const users = await readData('users')
    
    // 检查匹配状态
    const matches = await readData('matches')
    
    const likesList = receivedLikes.map(like => {
      const user = users.find(u => u.id == like.userId)
      
      // 检查是否已匹配
      const isMatched = matches.some(match => 
        (match.user1Id == currentUserId && match.user2Id == like.userId) ||
        (match.user2Id == currentUserId && match.user1Id == like.userId)
      )
      
      return {
        id: like.id,
        userId: like.userId,
        username: user?.username || '未知用户',
        nickname: user?.nickname,
        avatar: user?.avatar || '/uploads/default-avatar.png',
        likeTime: like.likeTime,
        isMatched
      }
    })

    // 按喜欢时间倒序排列
    likesList.sort((a, b) => new Date(b.likeTime) - new Date(a.likeTime))

    res.json({
      code: 0,
      data: likesList
    })

  } catch (error) {
    console.error('获取喜欢我的用户列表失败:', error)
    res.status(500).json({
      code: 500,
      message: '获取数据失败'
    })
  }
})

/**
 * 获取我喜欢的用户列表
 * GET /api/likes/sent
 */
router.get('/sent', async (req, res) => {
  try {
    const currentUserId = req.user.id

    // 获取我喜欢的记录
    const likes = await readData('likes')
    const sentLikes = likes.filter(like => 
      like.userId == currentUserId && like.action === 'like'
    )

    // 获取用户信息
    const users = await readData('users')
    
    // 检查匹配状态
    const matches = await readData('matches')
    
    const likesList = sentLikes.map(like => {
      const user = users.find(u => u.id == like.targetUserId)
      
      // 检查是否已匹配
      const isMatched = matches.some(match => 
        (match.user1Id == currentUserId && match.user2Id == like.targetUserId) ||
        (match.user2Id == currentUserId && match.user1Id == like.targetUserId)
      )
      
      return {
        id: like.id,
        userId: like.targetUserId,
        username: user?.username || '未知用户',
        nickname: user?.nickname,
        avatar: user?.avatar || '/uploads/default-avatar.png',
        likeTime: like.likeTime,
        isMatched
      }
    })

    // 按喜欢时间倒序排列
    likesList.sort((a, b) => new Date(b.likeTime) - new Date(a.likeTime))

    res.json({
      code: 0,
      data: likesList
    })

  } catch (error) {
    console.error('获取我喜欢的用户列表失败:', error)
    res.status(500).json({
      code: 500,
      message: '获取数据失败'
    })
  }
})

/**
 * 喜欢用户
 * POST /api/match/like
 */
router.post('/like', async (req, res) => {
  try {
    const { targetUserId } = req.body
    const currentUserId = req.user.id

    if (!targetUserId) {
      return res.status(400).json({
        code: 2001,
        message: '缺少目标用户ID'
      })
    }

    // 检查目标用户是否存在
    const users = await readData('users')
    const targetUser = users.find(u => u.id == targetUserId)
    
    if (!targetUser) {
      return res.status(404).json({
        code: 4001,
        message: '目标用户不存在'
      })
    }

    // 检查是否已经操作过
    const likes = await readData('likes')
    const existingAction = likes.find(like => 
      like.userId == currentUserId && like.targetUserId == targetUserId
    )

    if (existingAction) {
      return res.status(400).json({
        code: 2001,
        message: '已经操作过该用户'
      })
    }

    // 记录操作
    await addData('likes', {
      userId: currentUserId,
      targetUserId: parseInt(targetUserId),
      action: 'like',
      likeTime: new Date().toISOString()
    })

    let matched = false
    let matchId = null

    // 检查是否互相喜欢
    const mutualLike = likes.find(like => 
      like.userId == targetUserId && 
      like.targetUserId == currentUserId && 
      like.action === 'like'
    )

    if (mutualLike) {
      // 创建匹配记录
      const match = await addData('matches', {
        user1Id: Math.min(currentUserId, targetUserId),
        user2Id: Math.max(currentUserId, targetUserId),
        matchTime: new Date().toISOString(),
        lastActiveTime: new Date().toISOString()
      })

      matched = true
      matchId = match.id
    }

    res.json({
      code: 0,
      message: '操作成功',
      data: {
        matched,
        matchId
      }
    })

  } catch (error) {
    console.error('喜欢用户失败:', error)
    res.status(500).json({
      code: 500,
      message: '喜欢用户失败'
    })
  }
})

/**
 * 不喜欢用户
 * POST /api/match/dislike
 */
router.post('/dislike', async (req, res) => {
  try {
    const { targetUserId } = req.body
    const currentUserId = req.user.id

    if (!targetUserId) {
      return res.status(400).json({
        code: 2001,
        message: '缺少目标用户ID'
      })
    }

    // 检查目标用户是否存在
    const users = await readData('users')
    const targetUser = users.find(u => u.id == targetUserId)
    
    if (!targetUser) {
      return res.status(404).json({
        code: 4001,
        message: '目标用户不存在'
      })
    }

    // 检查是否已经操作过
    const likes = await readData('likes')
    const existingAction = likes.find(like => 
      like.userId == currentUserId && like.targetUserId == targetUserId
    )

    if (existingAction) {
      return res.status(400).json({
        code: 2001,
        message: '已经操作过该用户'
      })
    }

    // 记录操作
    await addData('likes', {
      userId: currentUserId,
      targetUserId: parseInt(targetUserId),
      action: 'dislike',
      likeTime: new Date().toISOString()
    })

    res.json({
      code: 0,
      message: '操作成功'
    })

  } catch (error) {
    console.error('不喜欢用户失败:', error)
    res.status(500).json({
      code: 500,
      message: '不喜欢用户失败'
    })
  }
})

/**
 * 检查匹配状态
 * GET /api/match/status/:userId
 */
router.get('/status/:userId', async (req, res) => {
  try {
    const targetUserId = parseInt(req.params.userId)
    const currentUserId = req.user.id

    if (!targetUserId || isNaN(targetUserId)) {
      return res.status(400).json({
        code: 2001,
        message: '无效的用户ID'
      })
    }

    // 检查是否已匹配
    const matches = await readData('matches')
    const isMatched = matches.some(match => 
      (match.user1Id === Math.min(currentUserId, targetUserId) && 
       match.user2Id === Math.max(currentUserId, targetUserId))
    )

    res.json({
      code: 0,
      data: {
        isMatched
      }
    })

  } catch (error) {
    console.error('检查匹配状态失败:', error)
    res.status(500).json({
      code: 500,
      message: '检查匹配状态失败'
    })
  }
})

module.exports = router 