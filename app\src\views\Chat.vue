<template>
  <page-layout :show-back="true" hide-tab-bar hide-safe-area :title="pageTitle">
    <template #title>
      <div class="header-title">
        <div class="avatar-container" @click="viewProfile(contactId)">
          <avatar :src="contactInfo?.avatar" size="small" />
        </div>
        <div class="title-info">
          <span class="contact-name">{{ contactInfo?.nickname || contactInfo?.username || contactInfo?.name || '聊天' }}</span>
          <span v-if="showTypingIndicator" class="typing-indicator">正在输入...</span>
          <span v-else-if="!connectionStatus.isConnected" class="connection-status">{{ connectionStatus.statusText }}</span>
        </div>
      </div>
    </template>
    <template #header-right>
      <div class="header-actions">
        <button v-if="connectionStatus.canRetry && !connectionStatus.isConnected"
                class="retry-btn"
                @click="retryConnection">
          🔄
        </button>
        <button class="chat-options" @click="uiState.showOptions = true">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="1"/>
            <circle cx="12" cy="5" r="1"/>
            <circle cx="12" cy="19" r="1"/>
          </svg>
        </button>
      </div>
    </template>

    <!-- 连接状态横幅 -->
    <div v-if="uiState.connectionBannerVisible" class="connection-banner" :class="chatState.connectionStatus">
      <div class="banner-content">
        <span class="banner-icon">
          <svg v-if="chatState.connectionStatus === 'connecting'" class="spin" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 12a9 9 0 11-6.219-8.56"/>
          </svg>
          <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="3"/>
          </svg>
        </span>
        <span class="banner-text">{{ connectionStatus.statusText }}</span>
        <button v-if="connectionStatus.canRetry" class="banner-retry" @click="retryConnection">
          重试
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="chatState.isLoading" class="loading-container">
      <div class="skeleton-container">
        <div class="skeleton-date" />
        <div class="skeleton-message other" />
        <div class="skeleton-message self" />
        <div class="skeleton-message other" />
        <div class="skeleton-message other" />
        <div class="skeleton-message self" />
      </div>
    </div>

    <!-- 聊天消息区域 -->
    <div v-else ref="chatContainer" class="chat-container" @scroll="handleScroll">
      <!-- 加载更多历史消息指示器 -->
      <div v-if="chatState.isLoadingMore" class="loading-more">
        <div class="loading-spinner">
          <svg class="spin" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 12a9 9 0 11-6.219-8.56"/>
          </svg>
        </div>
        <span>加载历史消息...</span>
      </div>

      <!-- 消息分组列表 -->
      <div class="message-list">
        <template v-for="group in groupedMessages" :key="group.date">
          <!-- 日期分隔符 -->
          <div class="date-separator">
            <span class="date-text">{{ group.displayDate }}</span>
          </div>

          <!-- 该日期的消息 -->
          <template v-for="(message, index) in group.messages" :key="`${group.date}-${index}`">
            <!-- 对方消息 -->
            <div v-if="!message.isSelf"
                 class="message-row other-message"
                 :class="{ consecutive: message.isConsecutive }"
                 :data-message-id="message.id">
              <div v-if="message.showAvatar"
                   class="avatar-container"
                   @click="viewProfile(contactId)">
                <avatar :src="contactInfo?.avatar" size="small" />
              </div>
              <div v-else class="avatar-placeholder"></div>

              <div class="message-content">
                <div class="message-container">
                  <!-- 文本消息 -->
                  <div v-if="message.type === 'text'"
                       class="message-bubble"
                       @contextmenu.prevent="showMessageMenu(message, $event)">
                    <div class="message-text">{{ message.content }}</div>
                  </div>

                  <!-- 图片消息 -->
                  <div v-else-if="message.type === 'image'"
                       class="message-bubble image-bubble"
                       @contextmenu.prevent="showMessageMenu(message, $event)">
                    <div v-if="message.loading" class="image-loading">
                      <svg class="spin" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 12a9 9 0 11-6.219-8.56"/>
                      </svg>
                    </div>
                    <img v-else
                      :src="getMessageImageUrl(message.content)"
                      :class="{ loaded: message.loaded, error: message.error }"
                      @click="previewImage(message.content)"
                      @load="onImageLoad(message, index)"
                      @error="onImageError(message)"
                      alt="图片消息"
                    />
                  </div>
                </div>

                <div v-if="message.showTime" class="message-info">
                  <span class="message-time">{{ formatMessageTime(message.time) }}</span>
                </div>
              </div>
            </div>

            <!-- 自己消息 -->
            <div v-else
                 class="message-row self-message"
                 :class="{ consecutive: message.isConsecutive }"
                 :data-message-id="message.id">
              <div class="message-content">
                <div class="message-container">
                  <!-- 文本消息 -->
                  <div v-if="message.type === 'text'"
                       class="message-bubble"
                       @contextmenu.prevent="showMessageMenu(message, $event)">
                    <div class="message-text">{{ message.content }}</div>
                  </div>

                  <!-- 图片消息 -->
                  <div v-else-if="message.type === 'image'"
                       class="message-bubble image-bubble"
                       @contextmenu.prevent="showMessageMenu(message, $event)">
                    <div v-if="message.loading" class="image-loading">
                      <svg class="spin" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 12a9 9 0 11-6.219-8.56"/>
                      </svg>
                    </div>
                    <img v-else
                      :src="getMessageImageUrl(message.content)"
                      :class="{ loaded: message.loaded, error: message.error }"
                      @click="previewImage(message.content)"
                      @load="onImageLoad(message, index)"
                      @error="onImageError(message)"
                      alt="图片消息"
                    />
                  </div>
                </div>

                <div v-if="message.showTime" class="message-info">
                  <div class="message-status">
                    <span v-if="message.status === 'sending'" class="status-sending">
                      <svg class="spin" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 12a9 9 0 11-6.219-8.56"/>
                      </svg>
                      发送中
                    </span>
                    <span v-else-if="message.status === 'failed'" class="status-error" @click="retryFailedMessage(message.id)">
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <line x1="12" y1="8" x2="12" y2="12"/>
                        <line x1="12" y1="16" x2="12.01" y2="16"/>
                      </svg>
                      重试
                    </span>
                    <span v-else-if="message.status === 'sent'" class="status-sent">
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="20 6 9 17 4 12"/>
                      </svg>
                    </span>
                    <span v-else-if="message.status === 'read'" class="status-read">
                      <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polyline points="20 6 9 17 4 12"/>
                        <polyline points="16 10 9 17 4 12"/>
                      </svg>
                    </span>
                  </div>
                  <span class="message-time">{{ formatMessageTime(message.time) }}</span>
                </div>
              </div>

              <div v-if="message.showAvatar" class="avatar-container">
                <avatar :src="userStore.user?.avatar" size="small" />
              </div>
              <div v-else class="avatar-placeholder"></div>
            </div>
          </template>
        </template>
      </div>

      <!-- 输入提示指示器 -->
      <div v-if="showTypingIndicator" class="typing-indicator-container">
        <div class="typing-bubble">
          <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>

    <!-- 滚动到底部按钮 -->
    <transition name="fade">
      <button v-if="scrollState.showScrollToBottom"
              ref="scrollToBottomBtn"
              class="scroll-to-bottom"
              @click="scrollToBottom(true)">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="7 13 12 18 17 13"/>
          <polyline points="7 6 12 11 17 6"/>
        </svg>
        <span v-if="scrollState.hasNewMessages" class="new-message-badge">新消息</span>
      </button>
    </transition>

    <!-- 输入区域 -->
    <div class="input-container" :class="{ focused: uiState.isInputFocused, uploading: imageState.isUploading }">
      <!-- 上传进度条 -->
      <div v-if="imageState.isUploading" class="upload-progress">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: imageState.uploadProgress + '%' }"></div>
        </div>
        <span class="progress-text">{{ imageState.uploadProgress }}%</span>
      </div>

      <div class="input-row">
        <!-- 表情按钮 -->
        <button class="input-action emoji-btn"
                :class="{ active: uiState.showEmojiPanel }"
                @click="toggleEmojiPanel"
                :disabled="!connectionStatus.isConnected">
          <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10" />
            <path d="M8 14s1.5 2 4 2 4-2 4-2" />
            <line x1="9" y1="9" x2="9.01" y2="9" />
            <line x1="15" y1="9" x2="15.01" y2="9" />
          </svg>
        </button>

        <!-- 文本输入框 -->
        <div class="input-box" :class="{ error: !inputState.isValid && inputState.charCount > 0 }">
          <textarea
            ref="messageInput"
            v-model="messageState.inputMessage"
            placeholder="输入消息..."
            :maxlength="1000"
            :disabled="!connectionStatus.isConnected"
            @keydown="handleKeyDown"
            @input="handleInputChange"
            @focus="handleInputFocus"
            @blur="handleInputBlur"
            @paste="handlePaste"
          />
          <div v-if="inputState.charCount > 800" class="char-counter" :class="{ warning: inputState.charCount > 900 }">
            {{ inputState.charCount }}/{{ inputState.maxChars }}
          </div>
        </div>

        <!-- 图片按钮 -->
        <button class="input-action image-btn"
                @click="triggerImageUpload"
                :disabled="!connectionStatus.isConnected || imageState.isUploading">
          <svg v-if="!imageState.isUploading" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
            <circle cx="8.5" cy="8.5" r="1.5" />
            <polyline points="21 15 16 10 5 21" />
          </svg>
          <svg v-else class="spin" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 12a9 9 0 11-6.219-8.56"/>
          </svg>
          <input
            ref="imageInput"
            type="file"
            accept="image/jpeg,image/png,image/gif,image/webp"
            multiple
            style="display: none"
            @change="handleImageSelect"
          />
        </button>

        <!-- 发送按钮 -->
        <button class="input-action send-button"
                :class="{ active: inputState.canSend, sending: messageState.sendCooldown }"
                :disabled="!inputState.canSend"
                @click="sendMessage">
          <svg v-if="!messageState.sendCooldown" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="22" y1="2" x2="11" y2="13" />
            <polygon points="22 2 15 22 11 13 2 9 22 2" />
          </svg>
          <svg v-else class="spin" width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 12a9 9 0 11-6.219-8.56"/>
          </svg>
        </button>
      </div>

      <!-- 表情面板组件 -->
      <emoji-panel v-if="uiState.showEmojiPanel" @select="insertEmoji" @close="uiState.showEmojiPanel = false" />
    </div>

    <!-- 消息菜单 -->
    <div v-if="messageMenu.visible"
         class="message-menu"
         :style="{ top: messageMenu.y + 'px', left: messageMenu.x + 'px' }"
         @click.stop>
      <button v-if="messageMenu.message?.type === 'text'" @click="copyMessage(messageMenu.message.content)">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
          <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1"/>
        </svg>
        复制
      </button>
      <button v-if="messageMenu.message?.isSelf" @click="deleteMessage(messageMenu.message.id)">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="3 6 5 6 21 6"/>
          <path d="M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6m3 0V4a2 2 0 012-2h4a2 2 0 012 2v2"/>
        </svg>
        删除
      </button>
      <button @click="hideMessageMenu">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="18" y1="6" x2="6" y2="18"/>
          <line x1="6" y1="6" x2="18" y2="18"/>
        </svg>
        取消
      </button>
    </div>

    <!-- 选项菜单 -->
    <div v-if="uiState.showOptions" class="options-overlay" @click="uiState.showOptions = false">
      <div class="options-menu" @click.stop>
        <div class="options-header">
          <h3>聊天选项</h3>
          <button class="close-btn" @click="uiState.showOptions = false">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>
        </div>
        <div class="options-content">
          <button class="option-item" @click="viewProfile(contactId)">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M20 21v-2a4 4 0 00-4-4H8a4 4 0 00-4 4v2"/>
              <circle cx="12" cy="7" r="4"/>
            </svg>
            查看资料
          </button>
          <button class="option-item warning" @click="reportContact">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" y1="8" x2="12" y2="12"/>
              <line x1="12" y1="16" x2="12.01" y2="16"/>
            </svg>
            举报用户
          </button>
          <button class="option-item danger" @click="blockContact">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="4.93" y1="4.93" x2="19.07" y2="19.07"/>
            </svg>
            屏蔽用户
          </button>
        </div>
      </div>
    </div>

    <!-- 图片预览 -->
    <transition name="fade">
      <div v-if="imageState.previewImageUrl" class="image-preview" @click="closePreview">
        <div v-if="imageState.previewLoading" class="image-preview-loading" />
        <img
          v-lazy="imageState.previewImageUrl"
          :class="{ loaded: !imageState.previewLoading }"
          @load="imageState.previewLoading = false"
        />
      </div>
    </transition>

    <!-- 聊天选项菜单 -->
    <transition name="fade">
      <div v-if="uiState.showOptions" class="options-panel" @click="uiState.showOptions = false">
        <transition name="slide-up">
          <div v-if="uiState.showOptions" class="options-content" @click.stop>
            <div class="options-header">
              <span>聊天选项</span>
              <span class="options-close" @click="uiState.showOptions = false">×</span>
            </div>

            <div class="options-group">
              <div class="option-item" @click="viewProfile(contactId)">
                <div class="option-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                    <circle cx="12" cy="7" r="4" />
                  </svg>
                </div>
                <span>查看资料</span>
              </div>
              <div class="option-item" @click="clearChat">
                <div class="option-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <polyline points="3 6 5 6 21 6" />
                    <path
                      d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"
                    />
                  </svg>
                </div>
                <span>清空聊天记录</span>
              </div>
              <div class="option-item" @click="unmatchContact">
                <div class="option-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M18 6L6 18" />
                    <path d="M6 6l12 12" />
                  </svg>
                </div>
                <span>解除匹配</span>
              </div>
              <div class="option-item" @click="reportContact">
                <div class="option-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"
                    />
                    <line x1="12" y1="9" x2="12" y2="13" />
                    <line x1="12" y1="17" x2="12.01" y2="17" />
                  </svg>
                </div>
                <span>举报</span>
              </div>
              <div class="option-item" @click="blockContact">
                <div class="option-icon">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <line x1="4.93" y1="4.93" x2="19.07" y2="19.07" />
                  </svg>
                </div>
                <span>屏蔽此人</span>
              </div>
            </div>

            <div class="option-item cancel" @click="showOptions = false">取消</div>
          </div>
        </transition>
      </div>
    </transition>
  </page-layout>
</template>

<script setup>
import { ref, computed, reactive, onMounted, onUnmounted, nextTick, watch, onBeforeUnmount, onActivated, onDeactivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import PageLayout from '../components/PageLayout.vue'
import Avatar from '../components/Avatar.vue'
import EmojiPanel from '../components/EmojiPanel.vue'
import { useMessageStore } from '../stores/messageStore'
import { useUserStore } from '../stores/userStore'
import { getImageUrl } from '../config'

const route = useRoute()
const router = useRouter()
const messageStore = useMessageStore()
const userStore = useUserStore()

// DOM引用
const chatContainer = ref(null)
const messageInput = ref(null)
const imageInput = ref(null)
const messageListRef = ref(null)
const scrollToBottomBtn = ref(null)

// 性能优化相关
const SCROLL_THRESHOLD = 50
const LOAD_MORE_THRESHOLD = 100
const MESSAGE_BATCH_SIZE = 20
const DEBOUNCE_DELAY = 100
const MAX_INPUT_HEIGHT = 120
const AUTO_SCROLL_DELAY = 100

// 聊天状态管理
const chatState = reactive({
  isLoading: true,
  isLoadingHistory: false,
  isLoadingMore: false,
  hasMoreHistory: true,
  lastScrollTop: 0,
  isAtBottom: true,
  isTyping: false,
  connectionStatus: 'connected', // connected, connecting, disconnected
  retryCount: 0,
  maxRetries: 3,
  lastActiveTime: Date.now(),
  isPageVisible: true,
  autoScrollEnabled: true
})

// 消息状态管理
const messageState = reactive({
  inputMessage: '',
  isComposing: false,
  lastSentTime: 0,
  sendingQueue: [],
  failedMessages: new Set(),
  messageCache: new Map(),
  lastMessageId: null,
  typingTimer: null,
  sendCooldown: false
})

// 图片状态管理
const imageState = reactive({
  previewImageUrl: null,
  previewLoading: true,
  uploadProgress: 0,
  isUploading: false,
  uploadQueue: [],
  maxFileSize: 10 * 1024 * 1024, // 10MB
  supportedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
})

// UI状态管理
const uiState = reactive({
  showOptions: false,
  showEmojiPanel: false,
  showScrollToBottom: false,
  inputHeight: 24,
  maxInputHeight: MAX_INPUT_HEIGHT,
  isInputFocused: false,
  showTypingIndicator: false,
  connectionBannerVisible: false
})

// 性能监控
const performanceState = reactive({
  renderTime: 0,
  scrollTime: 0,
  messageCount: 0,
  lastRenderTime: 0,
  fps: 60,
  isLowPerformance: false
})

// 消息菜单状态
const messageMenu = reactive({
  visible: false,
  message: null,
  x: 0,
  y: 0
})

// 计算属性 - 联系人ID
const contactId = computed(() => {
  const id = Number(route.params.id)
  if (isNaN(id) || id <= 0) {
    console.error('❌ 无效的联系人ID:', route.params.id)
    return null
  }
  return id
})

// 计算属性 - 联系人信息
const contactInfo = computed(() => {
  if (!contactId.value) return null
  const contact = messageStore.getContactById(contactId.value)
  if (!contact) {
    console.warn('⚠️ 未找到联系人信息:', contactId.value)
  }
  return contact
})

// 计算属性 - 聊天消息（带缓存）
const chatMessages = computed(() => {
  if (!contactId.value) return []

  const messages = messageStore.getMessagesByContactId(contactId.value)
  performanceState.messageCount = messages.length

  // 更新最后消息ID
  if (messages.length > 0) {
    const lastMessage = messages[messages.length - 1]
    messageState.lastMessageId = lastMessage.id
  }

  return messages
})

// 计算属性 - 页面标题
const pageTitle = computed(() => {
  const name = contactInfo.value?.nickname || contactInfo.value?.username || '聊天'
  const unreadCount = messageStore.getContactById(contactId.value)?.unreadCount || 0
  return unreadCount > 0 ? `${name} (${unreadCount})` : name
})

// 计算属性 - 消息分组（按日期，优化版）
const groupedMessages = computed(() => {
  const startTime = performance.now()
  const messages = chatMessages.value
  const groups = []
  let currentGroup = null

  messages.forEach((message, index) => {
    const messageDate = new Date(message.time).toDateString()

    if (!currentGroup || currentGroup.date !== messageDate) {
      currentGroup = {
        date: messageDate,
        displayDate: formatDateGroup(message.time),
        messages: []
      }
      groups.push(currentGroup)
    }

    // 优化消息显示逻辑
    const prevMessage = messages[index - 1]
    const nextMessage = messages[index + 1]

    currentGroup.messages.push({
      ...message,
      index,
      showAvatar: shouldShowAvatar(message, prevMessage),
      showTime: shouldShowTime(message, nextMessage),
      showDateSeparator: shouldShowDateSeparator(message, prevMessage),
      isConsecutive: isConsecutiveMessage(message, prevMessage),
      canRetry: message.status === 'failed' && message.isSelf
    })
  })

  // 性能监控
  const endTime = performance.now()
  performanceState.renderTime = endTime - startTime
  performanceState.lastRenderTime = endTime

  return groups
})

// 输入状态
const inputState = computed(() => {
  const text = messageState.inputMessage.trim()
  const charCount = messageState.inputMessage.length
  const maxChars = 1000

  return {
    charCount,
    maxChars,
    isEmpty: text.length === 0,
    isValid: charCount <= maxChars,
    hasContent: text.length > 0,
    canSend: text.length > 0 &&
             charCount <= maxChars &&
             !messageState.sendCooldown &&
             chatState.connectionStatus === 'connected'
  }
})

// 发送按钮状态
const canSendMessage = computed(() => {
  return inputState.value.canSend
})

// 连接状态信息
const connectionStatus = computed(() => {
  const status = chatState.connectionStatus

  return {
    isConnected: status === 'connected',
    statusText: {
      'connecting': '连接中...',
      'connected': '已连接',
      'disconnected': '连接断开',
      'reconnecting': '重新连接中...',
      'failed': '连接失败'
    }[status] || '未知状态',
    canRetry: ['disconnected', 'failed'].includes(status)
  }
})

// 滚动状态
const scrollState = computed(() => {
  return {
    showScrollToBottom: !chatState.isAtBottom && chatMessages.value.length > 0,
    hasNewMessages: chatState.lastScrollTop > 0 && !chatState.isAtBottom
  }
})

// 显示输入提示
const showTypingIndicator = computed(() => {
  // 这里可以根据实际需求显示对方的输入状态
  // 可以基于 uiState.showTypingIndicator 或其他逻辑
  return uiState.showTypingIndicator && !messageState.isComposing
})



// 工具函数 - 防抖
const debounce = (func, delay) => {
  let timeoutId
  return (...args) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(null, args), delay)
  }
}

// 工具函数 - 节流
const throttle = (func, delay) => {
  let lastCall = 0
  return (...args) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return func.apply(null, args)
    }
  }
}

// 辅助函数 - 判断是否显示头像
const shouldShowAvatar = (currentMessage, prevMessage) => {
  if (!prevMessage) return true
  if (currentMessage.isSelf !== prevMessage.isSelf) return true

  const timeDiff = new Date(currentMessage.time) - new Date(prevMessage.time)
  return timeDiff > 5 * 60 * 1000 // 5分钟
}

// 辅助函数 - 判断是否显示时间
const shouldShowTime = (currentMessage, nextMessage) => {
  if (!nextMessage) return true
  if (currentMessage.isSelf !== nextMessage.isSelf) return true

  const timeDiff = new Date(nextMessage.time) - new Date(currentMessage.time)
  return timeDiff > 5 * 60 * 1000 // 5分钟
}

// 辅助函数 - 判断是否显示日期分隔符
const shouldShowDateSeparator = (currentMessage, prevMessage) => {
  if (!prevMessage) return true

  const currentDate = new Date(currentMessage.time).toDateString()
  const prevDate = new Date(prevMessage.time).toDateString()
  return currentDate !== prevDate
}

// 辅助函数 - 判断是否为连续消息
const isConsecutiveMessage = (currentMessage, prevMessage) => {
  if (!prevMessage) return false
  if (currentMessage.isSelf !== prevMessage.isSelf) return false

  const timeDiff = new Date(currentMessage.time) - new Date(prevMessage.time)
  return timeDiff <= 2 * 60 * 1000 // 2分钟内
}

// 辅助函数 - 获取连接状态文本
const getConnectionStatusText = (status, retryCount, maxRetries) => {
  switch (status) {
    case 'connecting':
      return retryCount > 0 ? `重连中... (${retryCount}/${maxRetries})` : '连接中...'
    case 'disconnected':
      return retryCount >= maxRetries ? '连接失败' : '连接已断开'
    default:
      return ''
  }
}

// 辅助函数 - 格式化日期分组
const formatDateGroup = (timestamp) => {
  const date = new Date(timestamp)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  const dateStr = date.toDateString()
  const todayStr = today.toDateString()
  const yesterdayStr = yesterday.toDateString()

  if (dateStr === todayStr) {
    return '今天'
  } else if (dateStr === yesterdayStr) {
    return '昨天'
  } else {
    const year = date.getFullYear()
    const currentYear = today.getFullYear()

    if (year === currentYear) {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    } else {
      return `${year}年${date.getMonth() + 1}月${date.getDate()}日`
    }
  }
}

// 辅助函数 - 格式化消息时间
const formatMessageTime = (timestamp) => {
  if (!timestamp) return ''

  try {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now - date

    // 1分钟内
    if (diff < 60000) {
      return '刚刚'
    }

    // 1小时内
    if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`
    }

    // 今天
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })
    }

    // 昨天
    const yesterday = new Date(now)
    yesterday.setDate(yesterday.getDate() - 1)
    if (date.toDateString() === yesterday.toDateString()) {
      return `昨天 ${date.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })}`
    }

    // 更早
    return date.toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  } catch (error) {
    console.error('时间格式化错误:', error)
    return ''
  }
}

// 辅助函数 - 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}





// 页面可见性变化处理
const handleVisibilityChange = () => {
  if (!contactId.value) return

  if (document.visibilityState === 'visible') {
    console.log('📱 页面重新可见，标记已读')
    messageStore.markAsRead(contactId.value)
    chatState.connectionStatus = messageStore.socketConnected ? 'connected' : 'disconnected'
  } else {
    console.log('📱 页面不可见，最后标记已读')
    messageStore.markAsRead(contactId.value)
  }
}

// 滚动相关函数
const scrollToBottom = (smooth = false) => {
  nextTick(() => {
    if (chatContainer.value) {
      const scrollOptions = {
        top: chatContainer.value.scrollHeight,
        behavior: smooth ? 'smooth' : 'auto'
      }
      chatContainer.value.scrollTo(scrollOptions)
      chatState.isAtBottom = true
      uiState.showScrollToBottom = false
    }
  })
}

// 滚动处理 - 优化版本
const handleScroll = throttle(() => {
  if (!chatContainer.value) return

  const startTime = performance.now()
  const { scrollTop, scrollHeight, clientHeight } = chatContainer.value
  const isAtBottom = scrollTop + clientHeight >= scrollHeight - SCROLL_THRESHOLD

  // 更新滚动状态
  chatState.isAtBottom = isAtBottom
  chatState.lastScrollTop = scrollTop
  uiState.showScrollToBottom = !isAtBottom && chatMessages.value.length > 0

  // 检查是否需要加载更多历史消息
  if (scrollTop < LOAD_MORE_THRESHOLD && !chatState.isLoadingMore && chatState.hasMoreHistory) {
    loadMoreHistory()
  }

  // 性能监控
  const endTime = performance.now()
  performanceState.scrollTime = endTime - startTime

  // 如果滚动性能差，启用低性能模式
  if (performanceState.scrollTime > 16) { // 超过一帧时间
    performanceState.isLowPerformance = true
  }
}, DEBOUNCE_DELAY)



// 滚动到指定消息
const scrollToMessage = (messageId) => {
  const messageElement = document.querySelector(`[data-message-id="${messageId}"]`)
  if (messageElement && chatContainer.value) {
    messageElement.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    })
  }
}

// 加载更多历史消息 - 优化版本
const loadMoreHistory = async () => {
  if (!contactId.value || chatState.isLoadingMore || !chatState.hasMoreHistory) {
    return
  }

  chatState.isLoadingMore = true
  const oldScrollHeight = chatContainer.value?.scrollHeight || 0
  const oldScrollTop = chatContainer.value?.scrollTop || 0

  try {
    console.log('📚 加载更多历史消息，当前消息数:', chatMessages.value.length)

    const hasMore = await messageStore.loadMoreChatHistory(contactId.value, MESSAGE_BATCH_SIZE)
    chatState.hasMoreHistory = hasMore

    // 保持滚动位置 - 优化版本
    await nextTick()

    if (chatContainer.value) {
      const newScrollHeight = chatContainer.value.scrollHeight
      const scrollDiff = newScrollHeight - oldScrollHeight

      // 使用requestAnimationFrame确保DOM更新完成
      requestAnimationFrame(() => {
        if (chatContainer.value) {
          chatContainer.value.scrollTop = oldScrollTop + scrollDiff
          console.log('📚 历史消息加载完成，调整滚动位置')
        }
      })
    }

  } catch (error) {
    console.error('📚 加载历史消息失败:', error)
    // 可以在这里添加用户提示
    showErrorToast('加载历史消息失败，请稍后重试')
  } finally {
    chatState.isLoadingMore = false
  }
}

// 错误提示函数
const showErrorToast = (message) => {
  // 这里可以集成toast组件
  console.error('💬 聊天错误:', message)
}

// 监听消息变化 - 优化版本
watch(
  chatMessages,
  (newMessages, oldMessages) => {
    const hasNewMessages = newMessages.length > oldMessages.length

    if (hasNewMessages) {
      const latestMessage = newMessages[newMessages.length - 1]

      // 如果是自己发送的消息或者用户在底部，自动滚动
      if (latestMessage?.isSelf || chatState.isAtBottom) {
        setTimeout(() => scrollToBottom(true), AUTO_SCROLL_DELAY)
      }

      // 如果是对方的新消息，标记为已读
      if (latestMessage && !latestMessage.isSelf && chatState.isPageVisible) {
        console.log('📖 收到新消息，标记为已读')
        nextTick(() => {
          messageStore.markAsRead(contactId.value)
        })
      }

      // 更新页面标题
      updatePageTitle()
    }
  },
  { deep: true }
)

// 更新页面标题
const updatePageTitle = () => {
  if (typeof document !== 'undefined') {
    document.title = pageTitle.value
  }
}

// 发送消息 - 优化版本
const sendMessage = async () => {
  const text = messageState.inputMessage.trim()

  // 验证输入
  if (!validateMessageInput(text)) {
    return
  }

  // 防止重复发送
  if (messageState.sendCooldown) {
    console.log('⏳ 发送冷却中，请稍后再试')
    return
  }

  try {
    // 设置发送冷却
    messageState.sendCooldown = true

    // 添加到发送队列
    const messageId = Date.now().toString()
    messageState.sendingQueue.push(messageId)

    console.log('📤 发送消息:', { contactId: contactId.value, text })

    // 发送消息
    await messageStore.sendMessage(contactId.value, text)

    // 清空输入框和重置状态
    resetInputState()

    // 立即滚动到底部
    scrollToBottom(true)

    // 更新最后发送时间
    messageState.lastSentTime = Date.now()

  } catch (error) {
    console.error('📤 发送消息失败:', error)
    showErrorToast('消息发送失败，请重试')
  } finally {
    // 移除发送队列和冷却
    messageState.sendingQueue = messageState.sendingQueue.filter(id => id !== messageId)
    setTimeout(() => {
      messageState.sendCooldown = false
    }, 1000) // 1秒冷却
  }
}

// 验证消息输入
const validateMessageInput = (text) => {
  if (text.length === 0) {
    console.log('⚠️ 消息内容为空')
    return false
  }

  if (text.length > 1000) {
    showErrorToast('消息内容过长，请控制在1000字符以内')
    return false
  }

  if (!contactId.value) {
    showErrorToast('联系人信息错误')
    return false
  }

  if (chatState.connectionStatus !== 'connected') {
    showErrorToast('网络连接异常，请稍后重试')
    return false
  }

  return true
}

// 重置输入状态
const resetInputState = () => {
  messageState.inputMessage = ''

  // 重置输入框高度
  if (messageInput.value) {
    messageInput.value.style.height = 'auto'
    messageInput.value.style.height = '24px'
    uiState.inputHeight = 24
  }

  // 关闭表情面板
  if (uiState.showEmojiPanel) {
    uiState.showEmojiPanel = false
  }
}

// 调整输入框高度 - 优化版本
const adjustTextareaHeight = debounce(() => {
  if (!messageInput.value) return

  const textarea = messageInput.value
  const minHeight = 24
  const maxHeight = uiState.maxInputHeight

  // 重置高度以获取正确的scrollHeight
  textarea.style.height = 'auto'

  // 计算新高度
  const newHeight = Math.min(Math.max(textarea.scrollHeight, minHeight), maxHeight)

  // 应用新高度
  textarea.style.height = newHeight + 'px'
  uiState.inputHeight = newHeight

  // 如果内容超出最大高度，显示滚动条
  textarea.style.overflowY = textarea.scrollHeight > maxHeight ? 'auto' : 'hidden'
}, 50)

// 切换表情面板
const toggleEmojiPanel = () => {
  uiState.showEmojiPanel = !uiState.showEmojiPanel

  // 如果打开表情面板，聚焦输入框
  if (uiState.showEmojiPanel) {
    nextTick(() => {
      messageInput.value?.focus()
    })
  }
}

// 插入表情
const insertEmoji = (emoji) => {
  if (!messageInput.value) return

  const textarea = messageInput.value
  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const text = messageState.inputMessage

  // 插入表情
  messageState.inputMessage = text.slice(0, start) + emoji + text.slice(end)

  // 恢复光标位置
  nextTick(() => {
    const newPosition = start + emoji.length
    textarea.setSelectionRange(newPosition, newPosition)
    textarea.focus()
    adjustTextareaHeight()
  })
}

// 处理输入框焦点
const handleInputFocus = () => {
  uiState.isInputFocused = true
  chatState.lastActiveTime = Date.now()
}

const handleInputBlur = () => {
  uiState.isInputFocused = false
}

// 处理键盘事件
const handleKeyDown = (event) => {
  // Enter发送消息（Shift+Enter换行）
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
    return
  }

  // Escape关闭表情面板
  if (event.key === 'Escape' && uiState.showEmojiPanel) {
    event.preventDefault()
    uiState.showEmojiPanel = false
    return
  }

  // 更新活跃时间
  chatState.lastActiveTime = Date.now()
}

// 处理输入变化
const handleInputChange = debounce(() => {
  adjustTextareaHeight()

  // 发送输入状态（如果需要）
  if (messageState.inputMessage.trim().length > 0) {
    sendTypingStatus(true)
  } else {
    sendTypingStatus(false)
  }
}, 300)

// 发送输入状态
const sendTypingStatus = (isTyping) => {
  if (messageState.isComposing === isTyping) return

  messageState.isComposing = isTyping

  // 清除之前的定时器
  if (messageState.typingTimer) {
    clearTimeout(messageState.typingTimer)
  }

  // 如果正在输入，设置定时器自动停止
  if (isTyping) {
    messageState.typingTimer = setTimeout(() => {
      sendTypingStatus(false)
    }, 3000)
  }

  // 这里可以发送输入状态到服务器
  console.log('⌨️ 输入状态:', isTyping ? '正在输入...' : '停止输入')
}

// 图片处理函数
const handleImageSelect = (event) => {
  const files = event.target.files
  if (!files || files.length === 0) return

  Array.from(files).forEach(file => {
    if (validateImageFile(file)) {
      uploadImage(file)
    }
  })

  // 清空input
  if (imageInput.value) {
    imageInput.value.value = ''
  }
}

// 验证图片文件
const validateImageFile = (file) => {
  // 检查文件类型
  if (!imageState.supportedTypes.includes(file.type)) {
    showErrorToast('不支持的图片格式，请选择 JPG、PNG、GIF 或 WebP 格式')
    return false
  }

  // 检查文件大小
  if (file.size > imageState.maxFileSize) {
    showErrorToast(`图片大小不能超过 ${formatFileSize(imageState.maxFileSize)}`)
    return false
  }

  return true
}

// 上传图片
const uploadImage = async (file) => {
  if (imageState.isUploading) {
    showErrorToast('正在上传图片，请稍后再试')
    return
  }

  imageState.isUploading = true
  imageState.uploadProgress = 0

  try {
    console.log('📷 开始上传图片:', file.name)

    // 创建FormData
    const formData = new FormData()
    formData.append('image', file)

    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (imageState.uploadProgress < 90) {
        imageState.uploadProgress += 10
      }
    }, 200)

    // 这里应该调用实际的上传API
    // const response = await uploadImageAPI(formData)

    // 模拟上传完成
    setTimeout(() => {
      clearInterval(progressInterval)
      imageState.uploadProgress = 100

      // 发送图片消息
      const imageUrl = URL.createObjectURL(file) // 临时URL，实际应该使用服务器返回的URL
      messageStore.sendMessage(contactId.value, imageUrl, 'image')

      imageState.isUploading = false
      imageState.uploadProgress = 0

      console.log('📷 图片上传完成')
    }, 1000)

  } catch (error) {
    console.error('📷 图片上传失败:', error)
    showErrorToast('图片上传失败，请重试')
    imageState.isUploading = false
    imageState.uploadProgress = 0
  }
}

// 预览图片
const previewImage = (imageUrl) => {
  imageState.previewImageUrl = getMessageImageUrl(imageUrl)
  imageState.previewLoading = true

  // 这里可以打开图片预览模态框
  console.log('🖼️ 预览图片:', imageUrl)
}

// 关闭图片预览
const closePreview = () => {
  imageState.previewImageUrl = null
  imageState.previewLoading = true
}

// 获取消息图片URL
const getMessageImageUrl = (imagePath) => {
  if (!imagePath) return ''

  // 如果是完整URL，直接返回
  if (imagePath.startsWith('http') || imagePath.startsWith('data:')) {
    return imagePath
  }

  // 否则拼接基础URL
  return getImageUrl(imagePath)
}

// 图片加载完成
const onImageLoad = (message, index) => {
  if (message) {
    message.loaded = true
    message.loading = false
  }
  console.log('🖼️ 图片加载完成:', index)
}

// 图片加载失败
const onImageError = (message) => {
  if (message) {
    message.loaded = false
    message.loading = false
    message.error = true
  }
  console.log('🖼️ 图片加载失败')
}



// 页面隐藏处理
const handlePageHide = () => {
  console.log('📱 页面即将隐藏，保存状态')

  // 最后一次标记已读
  if (contactId.value) {
    messageStore.markAsRead(contactId.value)
  }

  // 停止输入状态
  sendTypingStatus(false)
}

// 重试失败消息
const retryFailedMessage = async (messageId) => {
  try {
    console.log('🔄 重试发送消息:', messageId)
    await messageStore.retryMessage(messageId)
    messageState.failedMessages.delete(messageId)
  } catch (error) {
    console.error('🔄 重试发送失败:', error)
    showErrorToast('重试发送失败')
  }
}

// 删除消息
const deleteMessage = async (messageId) => {
  if (!confirm('确定要删除这条消息吗？')) return

  try {
    console.log('🗑️ 删除消息:', messageId)
    await messageStore.deleteMessage(messageId)
  } catch (error) {
    console.error('🗑️ 删除消息失败:', error)
    showErrorToast('删除消息失败')
  }
}

// 复制消息内容
const copyMessage = async (content) => {
  try {
    await navigator.clipboard.writeText(content)
    showSuccessToast('消息已复制到剪贴板')
  } catch (error) {
    console.error('📋 复制失败:', error)
    showErrorToast('复制失败')
  }
}

// 成功提示
const showSuccessToast = (message) => {
  console.log('✅ 成功:', message)
}

// 查看用户资料
const viewProfile = (userId) => {
  if (!userId) return
  router.push(`/profile/${userId}`)
}

// 举报联系人
const reportContact = async () => {
  if (!confirm('确定要举报此人吗？')) return

  try {
    console.log('🚨 举报联系人:', contactId.value)
    // 这里调用举报API
    showSuccessToast('举报已提交')
    uiState.showOptions = false
  } catch (error) {
    console.error('🚨 举报失败:', error)
    showErrorToast('举报失败，请重试')
  }
}

// 屏蔽联系人
const blockContact = async () => {
  if (!confirm('确定要屏蔽此人吗？屏蔽后将无法收到对方的消息。')) return

  try {
    console.log('🚫 屏蔽联系人:', contactId.value)
    // 这里调用屏蔽API
    await messageStore.blockContact(contactId.value)
    showSuccessToast('已屏蔽该用户')
    uiState.showOptions = false
    router.back()
  } catch (error) {
    console.error('🚫 屏蔽失败:', error)
    showErrorToast('屏蔽失败，请重试')
  }
}

// 清空聊天记录
const clearChat = () => {
  if (confirm('确定要清空聊天记录吗？')) {
    messageStore.clearChat(contactId.value)
    uiState.showOptions = false
  }
}

// 解除匹配
const unmatchContact = () => {
  if (confirm('确定要解除匹配吗？')) {
    alert('已解除匹配')
    uiState.showOptions = false
  }
}

// 触发图片上传
const triggerImageUpload = () => {
  if (imageInput.value) {
    imageInput.value.click()
  }
}





// 消息菜单处理
const showMessageMenu = (message, event) => {
  messageMenu.message = message
  messageMenu.x = event.clientX
  messageMenu.y = event.clientY
  messageMenu.visible = true

  // 点击其他地方关闭菜单
  document.addEventListener('click', hideMessageMenu, { once: true })
}

const hideMessageMenu = () => {
  messageMenu.visible = false
  messageMenu.message = null
}



// 处理粘贴事件
const handlePaste = (event) => {
  const items = event.clipboardData?.items
  if (!items) return

  for (let i = 0; i < items.length; i++) {
    const item = items[i]
    if (item.type.indexOf('image') !== -1) {
      const file = item.getAsFile()
      if (file && validateImageFile(file)) {
        uploadImage(file)
      }
      event.preventDefault()
      break
    }
  }
}

// 重试连接
const retryConnection = async () => {
  if (chatState.retryCount >= chatState.maxRetries) {
    showErrorToast('重试次数过多，请稍后再试')
    return
  }

  chatState.retryCount++
  chatState.connectionStatus = 'connecting'

  try {
    // 这里调用重连逻辑
    await messageStore.reconnect()
    chatState.connectionStatus = 'connected'
    chatState.retryCount = 0
  } catch (error) {
    console.error('🔄 重连失败:', error)
    chatState.connectionStatus = 'failed'
  }
}



// 监听器设置
// 监听输入框内容变化
watch(() => messageState.inputMessage, handleInputChange)

// 监听路由参数变化
watch(contactId, async (newContactId, oldContactId) => {
  if (newContactId !== oldContactId && newContactId) {
    console.log('📱 聊天联系人切换:', { from: oldContactId, to: newContactId })

    // 重置状态
    chatState.isLoading = true
    messageState.inputMessage = ''
    uiState.showEmojiPanel = false

    try {
      // 更新当前聊天联系人ID
      messageStore.setCurrentChatContact(newContactId)

      // 加载新的聊天记录
      await messageStore.loadChatHistory(newContactId)

      // 标记为已读
      messageStore.markAsRead(newContactId)

      // 滚动到底部
      nextTick(() => {
        scrollToBottom()
        chatState.isLoading = false
      })

    } catch (error) {
      console.error('📱 切换聊天失败:', error)
      chatState.isLoading = false
    }
  }
})

// 监听连接状态变化
watch(() => chatState.connectionStatus, (newStatus, oldStatus) => {
  console.log('🔗 连接状态变化:', { from: oldStatus, to: newStatus })

  if (newStatus === 'connected' && oldStatus === 'disconnected') {
    // 重连成功，刷新消息
    if (contactId.value) {
      messageStore.loadChatHistory(contactId.value)
    }
  }

  // 显示连接状态横幅
  uiState.connectionBannerVisible = newStatus !== 'connected'
})

// 监听页面可见性
watch(() => chatState.isPageVisible, (isVisible) => {
  if (isVisible && contactId.value) {
    // 页面可见时标记消息为已读
    messageStore.markAsRead(contactId.value)
    chatState.lastActiveTime = Date.now()
  }
})

// 生命周期钩子
onMounted(async () => {
  try {
    console.log('📱 Chat页面挂载，联系人ID:', contactId.value)

    if (!contactId.value) {
      console.error('❌ 无效的联系人ID')
      router.replace('/message')
      return
    }

    // 初始化页面状态
    chatState.isPageVisible = !document.hidden
    chatState.lastActiveTime = Date.now()

    // 设置当前聊天联系人ID
    messageStore.setCurrentChatContact(contactId.value)

    // 加载聊天记录
    await messageStore.loadChatHistory(contactId.value)

    // 标记为已读
    messageStore.markAsRead(contactId.value)

    // 添加事件监听器
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('pagehide', handlePageHide)
    window.addEventListener('beforeunload', handlePageHide)

    // 添加滚动监听
    if (chatContainer.value) {
      chatContainer.value.addEventListener('scroll', handleScroll, { passive: true })
    }

    // 延迟显示内容，确保DOM渲染完成
    setTimeout(() => {
      chatState.isLoading = false
      scrollToBottom()
      updatePageTitle()
    }, 300)

  } catch (error) {
    console.error('❌ 初始化聊天页面失败:', error)
    chatState.isLoading = false
    showErrorToast('加载聊天记录失败')
  }
})

// 组件激活时（keep-alive）
onActivated(() => {
  console.log('📱 Chat页面激活')
  chatState.isPageVisible = true
  chatState.lastActiveTime = Date.now()

  if (contactId.value) {
    messageStore.markAsRead(contactId.value)
  }
})

// 组件停用时（keep-alive）
onDeactivated(() => {
  console.log('📱 Chat页面停用')
  chatState.isPageVisible = false

  if (contactId.value) {
    messageStore.markAsRead(contactId.value)
  }

  // 停止输入状态
  sendTypingStatus(false)
})

// 组件卸载前
onBeforeUnmount(() => {
  console.log('📱 Chat页面即将卸载')

  // 清理定时器
  if (messageState.typingTimer) {
    clearTimeout(messageState.typingTimer)
  }

  // 最后一次标记已读
  if (contactId.value) {
    messageStore.markAsRead(contactId.value)
  }

  // 清除当前聊天联系人ID
  messageStore.clearCurrentChatContact()

  // 停止输入状态
  sendTypingStatus(false)

  // 移除事件监听器
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('pagehide', handlePageHide)
  window.removeEventListener('beforeunload', handlePageHide)

  if (chatContainer.value) {
    chatContainer.value.removeEventListener('scroll', handleScroll)
  }
})
</script>

<style scoped>
  .header-title {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
  }

  .header-title .avatar-container {
    cursor: pointer;
    flex-shrink: 0;
  }

  .title-info {
    display: flex;
    flex-direction: column;
    min-width: 0;
  }

  .contact-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .typing-indicator {
    font-size: 12px;
    color: #ff6b6b;
    animation: pulse 1.5s infinite;
  }

  .connection-status {
    font-size: 12px;
    color: #999;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .retry-btn,
  .chat-options {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: none;
    border-radius: 50%;
    cursor: pointer;
    color: #666;
    transition: background-color 0.2s;
  }

  .retry-btn:hover,
  .chat-options:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }

  /* 连接状态横幅 */
  .connection-banner {
    background-color: #ff9800;
    color: white;
    padding: 8px 16px;
    text-align: center;
    font-size: 14px;
    position: relative;
    z-index: 10;
  }

  .connection-banner.connecting {
    background-color: #2196f3;
  }

  .connection-banner.disconnected {
    background-color: #f44336;
  }

  .connection-banner.failed {
    background-color: #9e9e9e;
  }

  .banner-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .banner-icon svg.spin {
    animation: spin 1s linear infinite;
  }

  .banner-retry {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
  }

  /* 加载状态和骨架屏 */
  .loading-container {
    flex: 1;
    overflow-y: auto;
    margin: 0 -15px;
    padding: 0 15px 10px;
    background-color: #ffffff;
  }

  .skeleton-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    padding: 20px 0;
  }

  .skeleton-date {
    height: 20px;
    width: 80px;
    background-color: #f0f0f0;
    border-radius: 10px;
    margin: 10px auto;
    animation: pulse 1.5s infinite;
  }

  .skeleton-message {
    height: 40px;
    border-radius: 18px;
    background-color: #f0f0f0;
    animation: pulse 1.5s infinite;
  }

  .skeleton-message.other {
    width: 70%;
    align-self: flex-start;
    margin-left: 48px;
    border-top-left-radius: 4px;
  }

  .skeleton-message.self {
    width: 60%;
    align-self: flex-end;
    margin-right: 48px;
    border-top-right-radius: 4px;
  }

  @keyframes pulse {
    0% {
      opacity: 0.6;
    }
    50% {
      opacity: 1;
    }
    100% {
      opacity: 0.6;
    }
  }

  .chat-container {
    flex: 1;
    overflow-y: auto;
    margin: 0 -15px;
    padding: 0 15px 10px;
    background-color: #ffffff;
    position: relative;
  }

  /* 加载更多指示器 */
  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 16px;
    color: #666;
    font-size: 14px;
  }

  .loading-spinner svg.spin {
    animation: spin 1s linear infinite;
  }

  /* 日期分隔符 */
  .date-separator {
    text-align: center;
    margin: 16px 0;
  }

  .date-text {
    display: inline-block;
    padding: 4px 12px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 12px;
    font-size: 12px;
    color: #666;
  }

  /* 头像占位符 */
  .avatar-placeholder {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
  }

  /* 连续消息样式 */
  .message-row.consecutive {
    margin-top: 2px;
  }

  .message-row.consecutive .message-bubble {
    margin-top: 2px;
  }

  /* 滚动到底部按钮 */
  .scroll-to-bottom {
    position: absolute;
    bottom: 80px;
    right: 20px;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: #ff6b6b;
    color: white;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    transition: all 0.3s ease;
  }

  .scroll-to-bottom:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .new-message-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #f44336;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    white-space: nowrap;
  }

  /* 输入提示气泡 */
  .typing-indicator-container {
    padding: 8px 0;
    display: flex;
    justify-content: flex-start;
    margin-left: 48px;
  }

  .typing-bubble {
    background-color: #f0f0f0;
    border-radius: 18px;
    padding: 12px 16px;
    border-top-left-radius: 4px;
  }

  .typing-dots {
    display: flex;
    gap: 4px;
  }

  .typing-dots span {
    width: 6px;
    height: 6px;
    background-color: #999;
    border-radius: 50%;
    animation: typingDots 1.4s infinite ease-in-out;
  }

  .typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
  }

  .typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
  }

  @keyframes typingDots {
    0%, 80%, 100% {
      transform: scale(0.8);
      opacity: 0.5;
    }
    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .chat-date {
    text-align: center;
    font-size: 12px;
    color: #999;
    margin: 10px 0;
    padding: 4px 12px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
    display: inline-block;
    margin-left: auto;
    margin-right: auto;
    width: auto;
    position: relative;
    left: 50%;
    transform: translateX(-50%);
  }

  .message-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  /* 消息行 */
  .message-row {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    animation: fadeIn 0.3s ease;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 对方消息 */
  .other-message {
    align-self: flex-start;
    margin-right: 60px;
  }

  /* 自己消息 */
  .self-message {
    align-self: flex-end;
    margin-left: 60px;
    justify-content: flex-end;
  }

  /* 头像容器 */
  .avatar-container {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
  }

  /* 消息内容区 */
  .message-content {
    display: flex;
    flex-direction: column;
    max-width: 100%;
  }

  .self-message .message-content {
    align-items: flex-end;
  }

  /* 消息容器 */
  .message-container {
    display: flex;
    flex-direction: column;
    max-width: 100%;
  }

  /* 消息气泡 */
  .message-bubble {
    border-radius: 18px;
    padding: 10px 15px;
    position: relative;
    word-break: break-word;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    max-width: 100%;
    contain: content;
    transform: translate3d(0, 0, 0);
    will-change: transform;
    transition:
      transform 0.2s ease,
      opacity 0.2s ease;
  }

  .other-message .message-bubble {
    background-color: white;
    color: #333;
    border-top-left-radius: 4px;
  }

  .self-message .message-bubble {
    background-color: #ff6b6b;
    color: white;
    border-top-right-radius: 4px;
  }

  .message-text {
    font-size: 15px;
    line-height: 1.4;
  }

  .image-bubble {
    padding: 3px;
    background-color: transparent;
    overflow: hidden;
    box-shadow: none;
    position: relative;
  }

  .image-bubble img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 12px;
    display: block;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .image-bubble img.loaded {
    opacity: 1;
  }

  .image-loading {
    width: 80px;
    height: 80px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image-loading:after {
    content: '';
    width: 24px;
    height: 24px;
    border: 2px solid transparent;
    border-top-color: #ff6b6b;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  .message-info {
    font-size: 11px;
    color: #999;
    margin-top: 4px;
    display: flex;
    align-items: center;
    gap: 5px;
  }

  .self-message .message-info {
    flex-direction: row-reverse;
  }

  .message-time {
    color: #999;
  }

  .status-sending {
    color: #ff9800;
  }

  .status-error {
    color: #f44336;
  }

  .status-sent {
    color: #2196f3;
  }

  .status-read {
    color: #8bc34a;
  }

  .input-container {
    border-top: 1px solid #f0f0f0;
    background-color: #fff;
    position: relative;
    z-index: 20;
    box-sizing: border-box;
    margin-bottom: 0;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    will-change: transform;
    padding: 12px 15px;
  }

  .input-container.focused {
    border-top-color: rgba(255, 107, 107, 0.3);
  }

  .input-container.uploading {
    padding-top: 8px;
  }

  /* 上传进度条 */
  .upload-progress {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    padding: 0 4px;
  }

  .progress-bar {
    flex: 1;
    height: 4px;
    background-color: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
  }

  .progress-fill {
    height: 100%;
    background-color: #ff6b6b;
    transition: width 0.3s ease;
  }

  .progress-text {
    font-size: 12px;
    color: #666;
    min-width: 32px;
  }

  /* 输入行 */
  .input-row {
    display: flex;
    align-items: flex-end;
    gap: 8px;
    min-height: 44px;
  }

  .input-action {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: none;
    cursor: pointer;
    color: #666;
    border-radius: 50%;
    transition: all 0.2s;
    flex-shrink: 0;
  }

  .input-action:hover:not(:disabled) {
    background-color: #f5f5f5;
    transform: scale(1.05);
  }

  .input-action:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .input-action svg {
    width: 22px;
    height: 22px;
  }

  .input-action svg.spin {
    animation: spin 1s linear infinite;
  }

  .emoji-btn.active {
    background-color: rgba(255, 107, 107, 0.1);
    color: #ff6b6b;
  }

  .send-button.sending {
    background-color: rgba(255, 107, 107, 0.1);
  }

  .input-box {
    flex: 1;
    background-color: #f5f5f5;
    border-radius: 20px;
    padding: 10px 15px;
    transition: all 0.2s;
    min-height: 44px;
    display: flex;
    align-items: flex-start;
    position: relative;
  }

  .input-box:focus-within {
    box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.2);
    background-color: #f8f8f8;
  }

  .input-box.error {
    box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.3);
    background-color: rgba(244, 67, 54, 0.05);
  }

  .char-counter {
    position: absolute;
    bottom: 4px;
    right: 8px;
    font-size: 10px;
    color: #999;
    background-color: rgba(255, 255, 255, 0.8);
    padding: 2px 4px;
    border-radius: 4px;
  }

  .char-counter.warning {
    color: #ff9800;
  }

  .input-box textarea {
    width: 100%;
    border: none;
    background-color: transparent;
    resize: none;
    outline: none;
    font-size: 15px;
    line-height: 1.4;
    max-height: 120px;
    min-height: 24px;
    padding: 0;
    font-family: inherit;
  }

  .send-button {
    color: #ccc;
  }

  .send-button.active {
    color: #ff6b6b;
  }

  .send-button svg {
    fill: currentColor;
    stroke: none;
  }

  .image-preview {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .image-preview img {
    max-width: 90%;
    max-height: 90%;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .image-preview img.loaded {
    opacity: 1;
  }

  .image-preview-loading {
    width: 48px;
    height: 48px;
    position: absolute;
    border: 3px solid transparent;
    border-top-color: white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  /* 消息菜单 */
  .message-menu {
    position: fixed;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 120px;
    overflow: hidden;
  }

  .message-menu button {
    width: 100%;
    padding: 12px 16px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #333;
    transition: background-color 0.2s;
  }

  .message-menu button:hover {
    background-color: #f5f5f5;
  }

  .message-menu button:last-child {
    border-top: 1px solid #f0f0f0;
    color: #666;
  }

  /* 选项菜单覆盖层 */
  .options-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
  }

  .options-menu {
    background-color: white;
    border-radius: 12px;
    max-width: 400px;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .options-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  .options-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }

  .close-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    transition: background-color 0.2s;
  }

  .close-btn:hover {
    background-color: #f5f5f5;
  }

  .options-content {
    padding: 8px 0;
  }

  @keyframes slideUp {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }

  .option-item {
    width: 100%;
    padding: 16px 20px;
    border: none;
    background: none;
    text-align: left;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 15px;
    color: #333;
    transition: background-color 0.2s;
  }

  .option-item:hover {
    background-color: #f5f5f5;
  }

  .option-item.warning {
    color: #ff9800;
  }

  .option-item.danger {
    color: #f44336;
  }

  .option-item svg {
    width: 20px;
    height: 20px;
    flex-shrink: 0;
  }

  /* 过渡效果 */
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

  .panel-enter-active,
  .panel-leave-active {
    transition:
      transform 0.3s ease,
      opacity 0.3s ease;
  }

  .panel-enter-from,
  .panel-leave-to {
    transform: translateY(100%);
    opacity: 0;
  }

  .slide-up-enter-active,
  .slide-up-leave-active {
    transition: transform 0.3s ease;
  }

  .slide-up-enter-from,
  .slide-up-leave-to {
    transform: translateY(100%);
  }

  /* 消息动画 */
  .message-row {
    animation: fadeIn 0.3s ease;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 提升渲染性能相关CSS */
  .chat-container,
  .loading-container {
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
    will-change: transform; /* 提示浏览器将创建单独图层 */
    transform: translate3d(0, 0, 0); /* 促进硬件加速 */
    backface-visibility: hidden; /* 减少闪烁 */
  }

  /* 输入容器和面板优化 */
  .input-container,
  .emoji-panel,
  .options-panel {
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    will-change: transform;
  }
</style>
