<template>
  <div class="card" :style="cardStyle" :class="{ swiping: isSwiping }" @touchstart="onTouchStart"
    @touchmove="onTouchMove" @touchend="onTouchEnd" @mousedown="onMouseDown" @mousemove="onMouseMove"
    @mouseup="onMouseUp" @mouseleave="onMouseUp" @click="viewProfile">
    <div class="card-image">
      <div v-show="!imageLoaded" class="image-placeholder" />
      <img :src="imageSrc" :alt="`${username}的照片`" :class="{ 'image-loaded': imageLoaded }" @error="handleImageError"
        @load="handleImageLoad" />

      <!-- 图片导航按钮 -->
      <div v-if="showImageNav" class="image-nav">
        <button class="nav-button prev" @click="prevImage">
          <span>◀</span>
        </button>
        <button class="nav-button next" @click="nextImage">
          <span>▶</span>
        </button>
      </div>

      <!-- 图片指示器 -->
      <div v-if="showImageNav" class="image-indicators">
        <span v-for="(_, index) in totalImages" :key="index" class="indicator"
          :class="{ active: index === currentImageIndex }" @click.stop="goToImage(index)" />
      </div>
    </div>
    <div class="card-info">
      <div class="info-header">
        <div class="name-container">
          <div class="avatar">
            <img :src="avatarSrc" :alt="`${username}的头像`" class="avatar-img" />
          </div>
          <div class="name">{{ username }}</div>
        </div>
        <div class="gender-age">
          <span class="gender" :class="{ 'gender-male': gender === 'male', 'gender-female': gender === 'female' }">
            {{ genderText }}
          </span>
          <span class="age">{{ age }}岁</span>
          <span v-if="regionDisplay" class="region">{{ regionDisplay }}</span>
        </div>
      </div>
      <div class="info-detail">
        <div v-for="(tag, tagIndex) in tags" :key="tagIndex" class="tag">{{ tag }}</div>
      </div>
    </div>

    <!-- 滑动方向指示器 - 与滑动方向相反显示 -->
    <div v-show="swipeDirection === 'left'" class="swipe-indicator like" :style="likeIndicatorStyle">
      <i class="icon-heart">♥</i>
      <span>喜欢</span>
    </div>
    <div v-show="swipeDirection === 'right'" class="swipe-indicator dislike" :style="dislikeIndicatorStyle">
      <i class="icon-close">✕</i>
      <span>不喜欢</span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { getImageUrl } from '../config'
import { useRouter } from 'vue-router'

const props = defineProps({
  id: {
    type: [Number, String],
    required: true
  },
  index: {
    type: Number,
    required: true
  },
  username: {
    type: String,
    default: '用户名'
  },
  gender: {
    type: String,
    default: '', // 'male', 'female', '' (未知)
    validator: value => ['male', 'female', ''].includes(value)
  },
  age: {
    type: [Number, String],
    default: 25
  },
  provinceCode: {
    type: String,
    default: ''
  },
  cityCode: {
    type: String,
    default: ''
  },
  region: {
    type: String,
    default: ''
  },
  tags: {
    type: Array,
    default: () => ['程序员', '旅行', '摄影']
  },
  avatar: {
    type: String,
    default: ''
  },
  image: {
    type: String,
    default: ''
  },
  images: {
    type: Array,
    default: () => []
  },
  enableSwipe: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['swipe-left', 'swipe-right'])

// 计算性别显示文本
const genderText = computed(() => {
  if (props.gender === 'male') {
    return '男'
  }
  if (props.gender === 'female') {
    return '女'
  }
  return ''
})

// 地区显示
const regionDisplay = computed(() => {
  // 优先使用后端返回的格式化region字段
  if (props.region) {
    // 如果region格式为"省份 城市"，则只取城市部分
    const parts = props.region.trim().split(/\s+/)
    if (parts.length > 1) {
      // 有多个部分，取最后一个作为城市
      return parts[parts.length - 1]
    }
    // 只有一个部分，直接返回（可能是省/市级城市）
    return props.region
  }
  // 降级到provinceCode和cityCode（向后兼容）
  if (props.cityCode) {
    // 只返回城市代码，不包含省份
    return props.cityCode
  }
  if (props.provinceCode) {
    return props.provinceCode
  }
  return ''
})

// 图片相关
const currentImageIndex = ref(0)
const imageLoaded = ref(false)

// 获取当前显示的图片
const imageSrc = computed(() => {
  // 如果有多张图片，使用当前索引的图片
  if (props.images && props.images.length > 0) {
    return getImageUrl(props.images[currentImageIndex.value])
  }
  // 兼容单张图片的情况
  return getImageUrl(props.image || '/uploads/default-avatar.png')
})

// 获取头像图片（固定不变）
const avatarSrc = computed(() => {
  // 优先使用 avatar 字段作为头像
  if (props.avatar) {
    return getImageUrl(props.avatar)
  }
  // 其次使用 image 字段作为头像
  if (props.image) {
    return getImageUrl(props.image)
  }
  // 如果没有 image 字段，使用 images 数组的第一张图片
  if (props.images && props.images.length > 0) {
    return getImageUrl(props.images[0])
  }
  // 都没有的话使用默认图片
  return getImageUrl(null)
})

// 图片总数
const totalImages = computed(() => {
  if (props.images && props.images.length > 0) {
    return props.images.length
  }
  return props.image ? 1 : 0
})

// 是否显示图片导航
const showImageNav = computed(() => {
  return totalImages.value > 1
})

const fallbackImage = `data:image/svg+xml;charset=utf-8,%3Csvg xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22 width%3D%22320%22 height%3D%22400%22 viewBox%3D%220 0 320 400%22%3E%3Crect fill%3D%22%23f0f0f0%22 width%3D%22320%22 height%3D%22400%22%2F%3E%3Ctext fill%3D%22rgba%280%2C0%2C0%2C0.5%29%22 font-family%3D%22-apple-system%2CBlinkMacSystemFont%2CSegoe UI%2CRoboto%2CHelvetica%2CArial%2Csans-serif%22 font-size%3D%2224%22 dy%3D%2210.5%22 font-weight%3D%22bold%22 x%3D%2250%25%22 y%3D%2250%25%22 text-anchor%3D%22middle%22%3E${props.username || '推荐卡片 ' + props.index}%3C%2Ftext%3E%3C%2Fsvg%3E`

// 预加载图片
onMounted(() => {
  if (props.images && props.images.length > 0) {
    // 预加载第一张图片
    preloadImage(props.images[0])
  } else if (props.image) {
    preloadImage(props.image)
  }
})

// 预加载图片
const preloadImage = src => {
  if (!src) {
    return
  }

  const img = new Image()
  img.src = src
  img.onload = () => {
    imageLoaded.value = true
  }
  img.onerror = handleImageError
}

// 处理图片加载错误
const handleImageError = () => {
  imageSrc.value = fallbackImage
  imageLoaded.value = true
}

// 处理图片加载完成
const handleImageLoad = () => {
  imageLoaded.value = true
}

// 切换到下一张图片
const nextImage = event => {
  // 阻止事件冒泡，避免触发卡片点击
  if (event) {
    event.stopPropagation()
  }

  imageLoaded.value = false
  currentImageIndex.value = (currentImageIndex.value + 1) % totalImages.value
  preloadImage(imageSrc.value)
}

// 切换到上一张图片
const prevImage = event => {
  // 阻止事件冒泡，避免触发卡片点击
  if (event) {
    event.stopPropagation()
  }

  imageLoaded.value = false
  currentImageIndex.value = (currentImageIndex.value - 1 + totalImages.value) % totalImages.value
  preloadImage(imageSrc.value)
}

// 查看用户资料
const router = useRouter()
const viewProfile = () => {
  if (!isSwiping.value) {
    router.push(`/profile/${props.id}`)
  }
}

// 滑动状态
const isSwiping = ref(false)
const startX = ref(0)
const startY = ref(0)
const moveX = ref(0)
const moveY = ref(0)
const swipeDirection = ref(null)
const zIndexValue = ref(100 - props.index) // 初始z-index值
const swipeProgress = ref(0) // 滑动进度，用于动态调整指示器样式

// 监听index变化，更新z-index
watch(
  () => props.index,
  newIndex => {
    if (!isSwiping.value) {
      zIndexValue.value = 100 - newIndex
    }
  }
)

// 计算卡片样式
const cardStyle = computed(() => {
  if (isSwiping.value) {
    const rotate = moveX.value * 0.1 // 根据移动距离计算旋转角度
    return {
      transform: `translateX(${moveX.value}px) translateY(${moveY.value}px) rotate(${rotate}deg)`,
      transition: 'none',
      zIndex: 200 // 滑动中的卡片总是最高层级
    }
  }

  // 根据索引设置默认样式
  if (props.index === 1) {
    return { transform: 'translateY(0) scale(1)', zIndex: zIndexValue.value }
  } else if (props.index === 2) {
    return { transform: 'translateY(15px) scale(0.95)', zIndex: zIndexValue.value }
  } else if (props.index === 3) {
    return { transform: 'translateY(30px) scale(0.9)', zIndex: zIndexValue.value }
  }

  return { zIndex: zIndexValue.value }
})

// 计算喜欢指示器样式
const likeIndicatorStyle = computed(() => {
  if (swipeDirection.value !== 'left') {
    return {}
  }

  const threshold = 100
  const progress = Math.min(Math.abs(moveX.value) / threshold, 1)

  return {
    opacity: progress,
    transform: `rotate(-20deg) scale(${0.8 + progress * 0.2})`
  }
})

// 计算不喜欢指示器样式
const dislikeIndicatorStyle = computed(() => {
  if (swipeDirection.value !== 'right') {
    return {}
  }

  const threshold = 100
  const progress = Math.min(Math.abs(moveX.value) / threshold, 1)

  return {
    opacity: progress,
    transform: `rotate(-20deg) scale(${0.8 + progress * 0.2})`
  }
})

// 触摸开始事件
const onTouchStart = e => {
  if (props.index !== 1 || !props.enableSwipe) {
    return
  } // 只有顶部卡片可以滑动，且滑动功能开启时

  startX.value = e.touches[0].clientX
  startY.value = e.touches[0].clientY
  isSwiping.value = true
  zIndexValue.value = 200 // 设置最高层级
}

// 触摸移动事件
const onTouchMove = e => {
  if (!isSwiping.value) {
    return
  }

  const currentX = e.touches[0].clientX
  const currentY = e.touches[0].clientY

  moveX.value = currentX - startX.value
  moveY.value = currentY - startY.value

  updateSwipeDirection()
}

// 触摸结束事件
const onTouchEnd = () => {
  if (!isSwiping.value) {
    return
  }
  handleSwipeEnd()
}

// 鼠标按下事件
const onMouseDown = e => {
  if (props.index !== 1 || !props.enableSwipe) {
    return
  } // 只有顶部卡片可以滑动，且滑动功能开启时

  startX.value = e.clientX
  startY.value = e.clientY
  isSwiping.value = true
  zIndexValue.value = 200 // 设置最高层级
}

// 鼠标移动事件
const onMouseMove = e => {
  if (!isSwiping.value) {
    return
  }

  moveX.value = e.clientX - startX.value
  moveY.value = e.clientY - startY.value

  updateSwipeDirection()
}

// 鼠标释放事件
const onMouseUp = () => {
  if (!isSwiping.value) {
    return
  }
  handleSwipeEnd()
}

// 更新滑动方向
const updateSwipeDirection = () => {
  const threshold = 50

  if (moveX.value > threshold) {
    swipeDirection.value = 'right'
  } else if (moveX.value < -threshold) {
    swipeDirection.value = 'left'
  } else {
    swipeDirection.value = null
  }

  // 计算滑动进度
  swipeProgress.value = Math.min(Math.abs(moveX.value) / 100, 1)
}

// 处理滑动结束
const handleSwipeEnd = () => {
  const threshold = 100 // 滑动阈值

  if (moveX.value > threshold) {
    // 向右滑动 - 喜欢
    completeSwipe('right')
  } else if (moveX.value < -threshold) {
    // 向左滑动 - 不喜欢
    completeSwipe('left')
  } else {
    // 回到原位
    resetPosition()
  }
}

// 完成滑动
const completeSwipe = direction => {
  const screenWidth = window.innerWidth
  const endX = direction === 'right' ? screenWidth * 1.5 : -screenWidth * 1.5

  // 设置过渡效果
  moveX.value = endX
  isSwiping.value = false

  // 立即触发事件，不使用定时器
  if (direction === 'right') {
    emit('swipe-left')
  } else {
    emit('swipe-right')
  }

  // 立即重置状态
  resetPosition(false)
}

// 重置位置
const resetPosition = (animate = true) => {
  if (animate) {
    // 添加过渡动画但不使用定时器
    moveX.value = 0
    moveY.value = 0
    isSwiping.value = false
    swipeDirection.value = null
    swipeProgress.value = 0
    zIndexValue.value = 100 - props.index // 恢复原始z-index
  } else {
    // 立即重置
    moveX.value = 0
    moveY.value = 0
    isSwiping.value = false
    swipeDirection.value = null
    swipeProgress.value = 0
    zIndexValue.value = 100 - props.index // 恢复原始z-index
  }
}

// 跳转到指定索引的图片
const goToImage = index => {
  if (index >= 0 && index < totalImages.value) {
    imageLoaded.value = false
    currentImageIndex.value = index
    preloadImage(imageSrc.value)
  }
}
</script>

<style scoped>
.card {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition:
    transform 0.3s ease,
    opacity 0.3s ease;
  touch-action: none;
  /* 防止页面滚动 */
  left: 0;
  top: 0;
  will-change: transform, z-index;
  /* 优化性能 */
  backface-visibility: hidden;
  /* 优化3D性能 */
  user-select: none;
  /* 防止文本选择 */
}

.swiping {
  cursor: grabbing;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: none;
  /* 滑动时禁用过渡效果 */
}

.card-image {
  width: 100%;
  height: 80%;
  overflow: hidden;
  background-color: #f8f8f8;
  position: relative;
}

.image-placeholder {
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(110deg, #ececec 8%, #f5f5f5 18%, #ececec 33%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite linear;
  z-index: 1;
}

@keyframes shimmer {
  0% {
    background-position: 100% 0;
  }

  100% {
    background-position: -100% 0;
  }
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  opacity: 0;
  transition: opacity 0.3s ease;
  position: relative;
  z-index: 2;
}

.image-loaded {
  opacity: 1 !important;
}

.card-info {
  padding: 15px;
}

.info-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.name-container {
  display: flex;
  align-items: center;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 8px;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.name {
  font-size: 18px;
  font-weight: 600;
}

.gender-age {
  display: flex;
  align-items: center;
}

.gender {
  font-size: 14px;
  padding: 2px 8px;
  border-radius: 12px;
  margin-right: 6px;
}

.gender-male {
  background-color: #e6f7ff;
  color: #1890ff;
}

.gender-female {
  background-color: #fff0f6;
  color: #eb2f96;
}

.age {
  font-size: 16px;
  color: #666;
}

.region {
  font-size: 14px;
  color: #666;
  margin-left: 2px;
  display: inline-flex;
  align-items: center;
}

.region::before {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23666"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg>');
  background-size: contain;
  background-repeat: no-repeat;
  margin-right: 3px;
}

.info-detail {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  padding: 4px 12px;
  background-color: #f8f8f8;
  border-radius: 20px;
  font-size: 12px;
  margin-right: 8px;
  margin-bottom: 8px;
  color: #666;
}

/* 滑动指示器样式 */
.swipe-indicator {
  position: absolute;
  top: 40px;
  padding: 10px 20px;
  border-radius: 5px;
  display: flex;
  align-items: center;
  font-weight: bold;
  font-size: 18px;
  transform: rotate(-20deg);
  border: 3px solid;
  z-index: 10;
  /* 确保指示器在卡片内容之上 */
  pointer-events: none;
  /* 防止干扰触摸事件 */
  opacity: 0;
  transition:
    transform 0.2s ease,
    opacity 0.2s ease;
}

.swipe-indicator i {
  margin-right: 5px;
  font-size: 24px;
}

.swipe-indicator.like {
  right: 20px;
  color: #ffffff;
  border-color: #ff6b6b;
  background-color: #ff6b6b;
}

.swipe-indicator.dislike {
  left: 20px;
  color: #ffffff;
  border-color: #999;
  background-color: #999;
}

/* 图片导航按钮样式 */
.image-nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
  pointer-events: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
}

.nav-button {
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  pointer-events: auto;
  opacity: 0.7;
  transition:
    opacity 0.2s ease,
    background-color 0.2s ease;
}

.nav-button:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.5);
}

.nav-button:focus {
  outline: none;
}

.nav-button span {
  font-size: 14px;
}

/* 图片指示器样式 */
.image-indicators {
  position: absolute;
  bottom: 10px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 8px;
  z-index: 5;
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition:
    background-color 0.2s ease,
    transform 0.2s ease;
  pointer-events: auto;
}

.indicator.active {
  background-color: white;
  transform: scale(1.2);
}

.indicator:hover {
  background-color: rgba(255, 255, 255, 0.8);
}
</style>
