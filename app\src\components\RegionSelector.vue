<template>
  <div class="region-selector">
    <div class="selector-row">
      <!-- 省份选择 -->
      <div class="selector-item">
        <select 
          v-model="selectedProvince" 
          class="selector-input"
          @change="handleProvinceChange"
        >
          <option value="" disabled>请选择省份</option>
          <option 
            v-for="province in provinces" 
            :key="province.code" 
            :value="province.code"
          >
            {{ province.name }}
          </option>
        </select>
      </div>

      <!-- 城市选择 -->
      <div class="selector-item">
        <select 
          v-model="selectedCity" 
          class="selector-input"
          :disabled="!selectedProvince || cities.length === 0"
          @change="handleCityChange"
        >
          <option value="" disabled>{{ cityPlaceholder }}</option>
          <option 
            v-for="city in cities" 
            :key="city.code" 
            :value="city.code"
          >
            {{ city.name }}
          </option>
        </select>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { userService } from '../services/userService'

const props = defineProps({
  provinceCode: {
    type: String,
    default: ''
  },
  cityCode: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请选择地区'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:provinceCode', 'update:cityCode', 'change'])

// 省市数据
const provinces = ref([])
const citiesMap = ref({}) // 所有城市数据的映射
const cities = ref([]) // 当前选中省份的城市列表

// 选中的值
const selectedProvince = ref('')
const selectedCity = ref('')

// 城市选择框的占位文本
const cityPlaceholder = computed(() => {
  if (!selectedProvince.value) return '请先选择省份'
  if (cities.value.length === 0) return '加载中...'
  return '请选择城市'
})

// 初始化数据
const initData = async () => {
  try {
    const response = await userService.getRegions()
    
    // 检查响应格式
    const data = response && response.data ? response.data : response
    
    if (data && data.provinces && data.cities) {
      provinces.value = data.provinces
      citiesMap.value = data.cities
      
      // 如果有初始省市代码，设置选中值
      if (props.provinceCode || props.cityCode) {
        selectedProvince.value = props.provinceCode || ''
        selectedCity.value = props.cityCode || ''
        if (props.provinceCode) {
          loadCities(props.provinceCode)
        }
      }
    }
  } catch (error) {
    console.error('获取地区数据失败:', error)
  }
}



// 加载城市数据
const loadCities = async (provinceCode) => {
  if (!provinceCode) {
    cities.value = []
    return
  }
  
  // 如果已经有缓存的城市数据
  if (citiesMap.value[provinceCode]) {
    cities.value = citiesMap.value[provinceCode]
    return
  }
  
  // 否则从服务器获取
  try {
    const response = await userService.getRegions(provinceCode)
    
    // 检查响应格式
    const data = response && response.data ? response.data : response
    
    if (data && Array.isArray(data)) {
      cities.value = data
      // 更新缓存
      citiesMap.value[provinceCode] = data
    } else {
      cities.value = []
    }
  } catch (error) {
    console.error('获取城市数据失败:', error)
    cities.value = []
  }
}

// 处理省份变化
const handleProvinceChange = () => {
  selectedCity.value = ''
  loadCities(selectedProvince.value)
  updateValue()
}

// 处理城市变化
const handleCityChange = () => {
  updateValue()
}

// 更新值并触发事件
const updateValue = () => {
  // 发出所有相关事件
  emit('update:provinceCode', selectedProvince.value)
  emit('update:cityCode', selectedCity.value)
  emit('change', {
    provinceCode: selectedProvince.value,
    cityCode: selectedCity.value
  })
}



// 监听外部省市代码变化
watch(() => [props.provinceCode, props.cityCode], ([newProvinceCode, newCityCode]) => {
  if (newProvinceCode !== selectedProvince.value || newCityCode !== selectedCity.value) {
    selectedProvince.value = newProvinceCode || ''
    selectedCity.value = newCityCode || ''
    
    if (newProvinceCode) {
      loadCities(newProvinceCode)
    } else {
      cities.value = []
    }
  }
}, { immediate: true })



// 组件挂载时初始化数据
onMounted(() => {
  initData()
})
</script>

<style scoped>
.region-selector {
  width: 100%;
  display: block;
}

.selector-row {
  display: flex;
  gap: 15px;
  width: 100%;
}

.selector-item {
  flex: 1;
  position: relative;
  width: 100%;
}

.selector-input {
  width: 100%;
  height: 45px;
  padding: 10px 0;
  border: none;
  border-bottom: 2px solid #f0f0f0;
  background-color: transparent;
  font-size: 16px;
  color: #333;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23999' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 5px center;
  background-size: 12px;
  transition: all 0.3s;
  outline: none;
  box-sizing: border-box;
}

.selector-input:focus {
  border-bottom-color: #ff8a8a;
}

.selector-input:hover:not(:disabled) {
  border-bottom-color: #ffb3b3;
}

.selector-input:disabled {
  background-color: transparent;
  color: #999;
  cursor: not-allowed;
  opacity: 0.7;
}

/* 自定义下拉箭头样式 */
.selector-input::-ms-expand {
  display: none;
}

/* 选项样式 */
.selector-input option {
  background-color: white;
  color: #333;
  padding: 10px;
}

@media (max-width: 480px) {
  .selector-row {
    gap: 10px;
  }
  
  .selector-input {
    font-size: 14px;
    height: 40px;
  }
}
</style> 