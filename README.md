# 社交匹配应用

一个现代化的社交匹配应用，包含前端（Vue 3 + Vite）和后端（Node.js + Express）。

## 🚀 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0

### 1. 启动后端服务

```bash
# 进入服务端目录
cd serve

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

后端服务将在 `http://localhost:3000` 启动

### 2. 启动前端应用

```bash
# 打开新终端，进入前端目录
cd app

# 安装依赖
npm install

# 环境配置文件已预配置，如需自定义可参考 .env.example

# 启动开发服务器
npm run dev
```

前端应用将在 `http://localhost:5173` 启动

### 3. 访问应用

打开浏览器访问：`http://localhost:5173`

默认测试账号：
- 用户名：`admin`，密码：`admin123`
- 用户名：`user1`，密码：`123456`

## 📁 项目结构

```
├── app/                    # 前端应用 (Vue 3 + Vite)
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── views/          # 页面
│   │   ├── services/       # API服务
│   │   ├── stores/         # 状态管理
│   │   ├── router/         # 路由配置
│   │   └── utils/          # 工具函数
│   └── package.json
├── serve/                  # 后端服务 (Node.js + Express)
│   ├── routes/             # API路由
│   ├── middleware/         # 中间件
│   ├── utils/              # 工具函数
│   ├── data/               # JSON数据存储
│   ├── uploads/            # 文件上传目录
│   └── package.json
└── api.md                  # API文档
```

## 🎯 主要功能

### 已完成功能

- ✅ 用户注册/登录/登出
- ✅ 用户资料管理
- ✅ 头像上传
- ✅ 智能匹配推荐
- ✅ 喜欢/不喜欢操作
- ✅ 双向匹配机制
- ✅ 实时聊天 (WebSocket)
- ✅ 动态发布/点赞/评论
- ✅ 文件上传服务
- ✅ JWT认证
- ✅ JSON数据存储

### 技术特性

- 🎨 现代化 Vue 3 组合式 API
- ⚡ Vite 构建工具
- 🔐 JWT Token 认证
- 💬 WebSocket 实时通信
- 📱 响应式设计
- 🗃️ JSON 文件数据存储
- 🔒 安全的文件上传
- 📊 完整的API文档

## 🛠️ 开发指南

### 后端开发

服务端使用 Express.js 框架，基于JSON文件存储数据：

```bash
cd serve

# 开发模式（自动重启）
npm run dev

# 生产模式
npm start
```

API地址：`http://localhost:3000/api`
WebSocket：`ws://localhost:3000/ws`

### 前端开发

前端使用 Vue 3 + Vite：

```bash
cd app

# 开发模式（热重载）
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

### 环境配置

项目已提供完整的环境变量配置支持，消除了硬编码问题。

#### 前端环境变量

前端配置文件位于 `app/.env.development`，支持以下配置：

```bash
# 网络配置
VITE_API_HOST=localhost
VITE_API_PORT=3000
VITE_API_BASE_URL=http://localhost:3000/api
VITE_WS_URL=ws://localhost:3000/ws

# 应用配置
VITE_APP_TITLE=社交匹配应用
VITE_REQUEST_TIMEOUT=60000
VITE_PAGE_SIZE=20

# 文件上传配置
VITE_MAX_FILE_SIZE=10485760
VITE_MAX_PHOTOS_COUNT=6
```

完整配置请参考 `app/.env.example`

#### 后端环境变量

后端配置文件位于 `serve/.env`，支持以下配置：

```bash
# 服务器配置
PORT=3000
NODE_ENV=development
JWT_SECRET=your-secret-key-here

# CORS和限流配置
CORS_ORIGIN=http://localhost:5173,http://localhost:3000
RATE_LIMIT_MAX=1000

# 文件上传配置
MAX_FILE_SIZE=10485760
MAX_PHOTOS_COUNT=6
```

完整配置请参考 `serve/.env.example`

## 📡 API 文档

详细的API文档请查看 [api.md](./api.md)

主要接口：

- **认证**: `/api/auth/login`, `/api/auth/register`
- **用户**: `/api/user/profile`, `/api/user/avatar`
- **匹配**: `/api/matches/recommendations`, `/api/matches/action`
- **消息**: `/api/messages/contacts`, `/api/messages/send`
- **帖子**: `/api/posts`, `/api/posts/:id/like`
- **上传**: `/api/upload/single`, `/api/upload/avatar`

## 🔧 数据存储

项目使用JSON文件作为轻量级数据存储，位于 `serve/data/` 目录：

- `users.json` - 用户数据
- `posts.json` - 帖子数据
- `messages.json` - 消息数据
- `matches.json` - 匹配数据
- `likes.json` - 点赞数据
- `comments.json` - 评论数据
- `uploads.json` - 上传文件记录

## 🧪 测试账号

系统预置了测试账号，可以直接使用：

1. **管理员账号**
   - 用户名：`admin`
   - 密码：`admin123`

2. **测试用户**
   - 用户名：`user1` 
   - 密码：`123456`

## 🚀 部署指南

### 生产环境部署

1. **后端部署**
```bash
cd serve
npm ci --only=production
npm start
```

2. **前端构建**
```bash
cd app
npm ci
npm run build
```

3. **环境配置**
   - 设置正确的API地址
   - 配置JWT密钥
   - 设置CORS允许的域名

### Docker 部署（可选）

```dockerfile
# 后端 Dockerfile
FROM node:16-alpine
WORKDIR /app
COPY serve/package*.json ./
RUN npm ci --only=production
COPY serve/ .
EXPOSE 3000
CMD ["npm", "start"]
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :3000
   lsof -i :5173
   ```

2. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   npm cache clean --force
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **WebSocket连接失败**
   - 检查后端服务是否正常启动
   - 确认WebSocket地址配置正确
   - 检查浏览器控制台错误信息

4. **API请求失败**
   - 检查API地址配置
   - 确认后端服务运行正常
   - 检查CORS配置

### 开发工具推荐

- **API测试**: Postman, Insomnia
- **WebSocket测试**: WebSocket King
- **代码编辑器**: VS Code
- **浏览器**: Chrome DevTools

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 完成基础用户认证系统
- ✅ 实现匹配推荐算法
- ✅ 添加实时聊天功能
- ✅ 完成动态发布系统
- ✅ 实现文件上传功能
- ✅ 替换所有模拟数据为真实API

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 技术支持

如果你在使用过程中遇到问题，可以：

1. 查看本文档的故障排除部分
2. 检查 GitHub Issues
3. 提交新的 Issue

---

**祝你使用愉快！** 🎉 