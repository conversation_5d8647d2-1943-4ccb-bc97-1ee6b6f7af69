const express = require('express')
const multer = require('multer')
const path = require('path')
const fs = require('fs-extra')
const { readData, addData, findData, paginateData } = require('../utils/dataStore')

const router = express.Router()

// 配置图片上传
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadPath = path.join(__dirname, '../uploads')
    await fs.ensureDir(uploadPath)
    cb(null, uploadPath)
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
    cb(null, 'chat-' + uniqueSuffix + path.extname(file.originalname))
  }
})

const upload = multer({ 
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true)
    } else {
      cb(new Error('只支持图片文件'))
    }
  }
})

/**
 * 获取联系人列表
 * GET /api/messages/contacts
 */
router.get('/contacts', async (req, res) => {
  try {
    const currentUserId = req.user.id

    // 获取匹配记录
    const matches = await readData('matches')
    const userMatches = matches.filter(match => 
      match.user1Id == currentUserId || match.user2Id == currentUserId
    )

    // 获取用户信息
    const users = await readData('users')
    
    // 获取最新消息
    const messages = await readData('messages')
    
    const contacts = userMatches.map(match => {
      const partnerId = match.user1Id == currentUserId ? match.user2Id : match.user1Id
      const partner = users.find(u => u.id == partnerId)
      
      // 获取与该用户的最新消息
      const chatMessages = messages.filter(msg => 
        (msg.senderId == currentUserId && msg.receiverId == partnerId) ||
        (msg.senderId == partnerId && msg.receiverId == currentUserId)
      )
      
      chatMessages.sort((a, b) => new Date(b.time) - new Date(a.time))
      const lastMessage = chatMessages[0]
      
      // 计算未读消息数
      const unreadCount = chatMessages.filter(msg => 
        msg.receiverId == currentUserId && !msg.read
      ).length
      
      return {
        id: partnerId,
        username: partner?.username || '未知用户',
        nickname: partner?.nickname,
        avatar: partner?.avatar || '/uploads/default-avatar.png',
        lastMessage: lastMessage ? {
          content: lastMessage.content,
          time: lastMessage.time,
          type: lastMessage.contentType || 'text'
        } : null,
        unreadCount,
        matchTime: match.matchTime
      }
    })

    // 按最后消息时间排序
    contacts.sort((a, b) => {
      const timeA = a.lastMessage ? new Date(a.lastMessage.time) : new Date(a.matchTime)
      const timeB = b.lastMessage ? new Date(b.lastMessage.time) : new Date(b.matchTime)
      return timeB - timeA
    })

    res.json({
      code: 0,
      data: contacts
    })

  } catch (error) {
    console.error('获取联系人列表失败:', error)
    res.status(500).json({
      code: 500,
      message: '获取联系人列表失败'
    })
  }
})

/**
 * 获取聊天记录
 * GET /api/messages/chat/:userId
 */
router.get('/chat/:userId', async (req, res) => {
  try {
    const currentUserId = req.user.id
    const targetUserId = parseInt(req.params.userId)
    const { page = 1, limit = 20 } = req.query

    // 验证是否为匹配用户
    const matches = await readData('matches')
    const isMatched = matches.some(match => 
      (match.user1Id == currentUserId && match.user2Id == targetUserId) ||
      (match.user2Id == currentUserId && match.user1Id == targetUserId)
    )

    if (!isMatched) {
      return res.status(403).json({
        code: 4003,
        message: '只能与匹配用户聊天'
      })
    }

    // 获取聊天记录
    const messages = await readData('messages')
    const chatMessages = messages.filter(msg => 
      (msg.senderId == currentUserId && msg.receiverId == targetUserId) ||
      (msg.senderId == targetUserId && msg.receiverId == currentUserId)
    )

    // 按时间倒序排列
    chatMessages.sort((a, b) => new Date(b.time) - new Date(a.time))

    // 分页
    const paginatedResult = paginateData(chatMessages, page, limit)

    // 将消息标记为已读
    const unreadMessages = messages.filter(msg => 
      msg.senderId == targetUserId && 
      msg.receiverId == currentUserId && 
      !msg.read
    )

    for (const msg of unreadMessages) {
      msg.read = true
      msg.readTime = new Date().toISOString()
    }

    if (unreadMessages.length > 0) {
      await require('../utils/dataStore').writeData('messages', messages)
    }

    res.json({
      code: 0,
      data: paginatedResult.data,
      pagination: paginatedResult.pagination
    })

  } catch (error) {
    console.error('获取聊天记录失败:', error)
    res.status(500).json({
      code: 500,
      message: '获取聊天记录失败'
    })
  }
})

/**
 * 发送消息
 * POST /api/messages/send
 */
router.post('/send', async (req, res) => {
  try {
    const { receiverId, content, contentType = 'text' } = req.body
    const senderId = req.user.id

    // 参数验证
    if (!receiverId || !content) {
      return res.status(400).json({
        code: 2001,
        message: '缺少必要参数'
      })
    }

    // 验证是否为匹配用户
    const matches = await readData('matches')
    const isMatched = matches.some(match => 
      (match.user1Id == senderId && match.user2Id == receiverId) ||
      (match.user2Id == senderId && match.user1Id == receiverId)
    )

    if (!isMatched) {
      return res.status(403).json({
        code: 4003,
        message: '只能向匹配用户发送消息'
      })
    }

    // 保存消息
    const message = await addData('messages', {
      senderId: senderId,
      receiverId: parseInt(receiverId),
      content,
      contentType,
      time: new Date().toISOString(),
      read: false
    })

    // 更新匹配的最后活跃时间
    const matchToUpdate = matches.find(match => 
      (match.user1Id == senderId && match.user2Id == receiverId) ||
      (match.user2Id == senderId && match.user1Id == receiverId)
    )

    if (matchToUpdate) {
      await require('../utils/dataStore').updateData('matches', matchToUpdate.id, {
        lastActiveTime: new Date().toISOString()
      })
    }

    // 通过WebSocket发送实时消息（如果接收者在线）
    if (req.app.broadcastToUser) {
      req.app.broadcastToUser(receiverId, {
        type: 'message',
        id: message.id,
        senderId,
        content,
        contentType,
        time: message.time
      })

      // 向发送者发送状态更新，确认消息已发送
      req.app.broadcastToUser(senderId, {
        type: 'status',
        messageId: message.id,
        status: 'sent'
      })
    }

    res.json({
      code: 0,
      message: '发送成功',
      data: message
    })

  } catch (error) {
    console.error('发送消息失败:', error)
    res.status(500).json({
      code: 500,
      message: '发送消息失败'
    })
  }
})

/**
 * 标记消息为已读（批量）
 * PUT /api/messages/read
 */
router.put('/read', async (req, res) => {
  try {
    const currentUserId = req.user.id
    const { contactId, messageIds } = req.body

    // 参数验证
    if (!contactId) {
      return res.status(400).json({
        code: 2001,
        message: '缺少联系人ID'
      })
    }

    const messages = await readData('messages')
    let readCount = 0

    if (messageIds && messageIds.length > 0) {
      // 按消息ID批量标记已读
      messageIds.forEach(messageId => {
        const message = messages.find(msg => 
          msg.id == messageId && 
          msg.receiverId == currentUserId && 
          !msg.read
        )
        if (message) {
          message.read = true
          message.readTime = new Date().toISOString()
          readCount++
        }
      })
    } else {
      // 标记与指定联系人的所有未读消息为已读
      const unreadMessages = messages.filter(msg => 
        msg.senderId == contactId && 
        msg.receiverId == currentUserId && 
        !msg.read
      )

      for (const msg of unreadMessages) {
        msg.read = true
        msg.readTime = new Date().toISOString()
        readCount++
      }
    }

    if (readCount > 0) {
      await require('../utils/dataStore').writeData('messages', messages)
    }

    res.json({
      code: 0,
      message: '标记已读成功',
      data: {
        readCount
      }
    })

  } catch (error) {
    console.error('标记已读失败:', error)
    res.status(500).json({
      code: 500,
      message: '标记已读失败'
    })
  }
})

/**
 * 标记消息为已读（单个联系人）
 * PUT /api/messages/read/:userId
 */
router.put('/read/:userId', async (req, res) => {
  try {
    const currentUserId = req.user.id
    const senderId = parseInt(req.params.userId)

    const messages = await readData('messages')
    
    // 找到未读消息并标记为已读
    const unreadMessages = messages.filter(msg => 
      msg.senderId == senderId && 
      msg.receiverId == currentUserId && 
      !msg.read
    )

    for (const msg of unreadMessages) {
      msg.read = true
      msg.readTime = new Date().toISOString()
    }

    if (unreadMessages.length > 0) {
      await require('../utils/dataStore').writeData('messages', messages)
    }

    res.json({
      code: 0,
      message: '标记已读成功',
      data: {
        readCount: unreadMessages.length
      }
    })

  } catch (error) {
    console.error('标记已读失败:', error)
    res.status(500).json({
      code: 500,
      message: '标记已读失败'
    })
  }
})

/**
 * 上传聊天图片
 * POST /api/messages/upload
 */
router.post('/upload', upload.single('image'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        code: 2001,
        message: '请选择图片文件'
      })
    }

    const imageUrl = `/uploads/${req.file.filename}`

    // 记录上传信息
    await addData('uploads', {
      userId: req.user.id,
      type: 'chat_image',
      filename: req.file.filename,
      originalName: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      url: imageUrl
    })

    res.json({
      code: 0,
      message: '图片上传成功',
      data: {
        url: imageUrl,
        size: req.file.size,
        mimeType: req.file.mimetype
      }
    })

  } catch (error) {
    console.error('上传聊天图片失败:', error)
    res.status(500).json({
      code: 500,
      message: '上传图片失败'
    })
  }
})

/**
 * 上传聊天图片
 * POST /api/messages/upload
 */
router.post('/upload', async (req, res) => {
  try {
    const multer = require('multer')
    const path = require('path')
    const fs = require('fs-extra')

    // 配置文件上传
    const storage = multer.diskStorage({
      destination: async (req, file, cb) => {
        const uploadPath = path.join(__dirname, '../uploads')
        await fs.ensureDir(uploadPath)
        cb(null, uploadPath)
      },
      filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
        cb(null, 'chat-' + uniqueSuffix + path.extname(file.originalname))
      }
    })

    const upload = multer({ 
      storage,
      limits: {
        fileSize: 5 * 1024 * 1024 // 5MB
      },
      fileFilter: (req, file, cb) => {
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
        if (allowedTypes.includes(file.mimetype)) {
          cb(null, true)
        } else {
          cb(new Error('只支持图片文件'))
        }
      }
    }).single('image')

    upload(req, res, async (err) => {
      if (err) {
        return res.status(400).json({
          code: 2001,
          message: err.message || '上传失败'
        })
      }

      if (!req.file) {
        return res.status(400).json({
          code: 2001,
          message: '请选择图片文件'
        })
      }

      const imageUrl = `/uploads/${req.file.filename}`

      // 记录上传信息
      const { addData } = require('../utils/dataStore')
      await addData('uploads', {
        userId: req.user.id,
        type: 'chat',
        filename: req.file.filename,
        originalName: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        url: imageUrl
      })

      res.json({
        code: 0,
        message: '上传成功',
        data: {
          url: imageUrl,
          size: req.file.size,
          mimeType: req.file.mimetype
        }
      })
    })

  } catch (error) {
    console.error('上传聊天图片失败:', error)
    res.status(500).json({
      code: 500,
      message: '上传失败'
    })
  }
})

module.exports = router 