/**
 * 应用配置
 */

// 默认配置常量
const DEFAULT_CONFIG = {
  // 网络配置
  API_PORT: '3000',
  DEV_PORT: '5173',
  HOST: 'localhost',

  // 超时配置
  REQUEST_TIMEOUT: 60000, // 60秒
  WS_RECONNECT_INTERVAL: 3000, // 3秒
  WS_MAX_RECONNECT_ATTEMPTS: 5,

  // 分页配置
  PAGE_SIZE: 20,

  // 文件上传配置
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_AVATAR_SIZE: 5 * 1024 * 1024, // 5MB
  MAX_PHOTOS_COUNT: 6,
  MAX_POST_IMAGES: 9,

  // 应用配置
  APP_TITLE: '社交匹配应用'
}

// 开发环境配置
const development = {
  API_BASE_URL: `http://${import.meta.env.VITE_API_HOST || DEFAULT_CONFIG.HOST}:${import.meta.env.VITE_API_PORT || DEFAULT_CONFIG.API_PORT}/api`,
  WS_URL: `ws://${import.meta.env.VITE_API_HOST || DEFAULT_CONFIG.HOST}:${import.meta.env.VITE_API_PORT || DEFAULT_CONFIG.API_PORT}/ws`,
  IMAGE_BASE_URL: `http://${import.meta.env.VITE_API_HOST || DEFAULT_CONFIG.HOST}:${import.meta.env.VITE_API_PORT || DEFAULT_CONFIG.API_PORT}`,
  APP_TITLE: import.meta.env.VITE_APP_TITLE || DEFAULT_CONFIG.APP_TITLE
}

// 生产环境配置
const production = {
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || '/api',
  WS_URL: import.meta.env.VITE_WS_URL || `ws://${window.location.host}/ws`,
  IMAGE_BASE_URL: import.meta.env.VITE_IMAGE_BASE_URL || '',
  APP_TITLE: import.meta.env.VITE_APP_TITLE || DEFAULT_CONFIG.APP_TITLE
}

// 测试环境配置
const test = {
  API_BASE_URL: `http://${import.meta.env.VITE_API_HOST || DEFAULT_CONFIG.HOST}:${import.meta.env.VITE_API_PORT || DEFAULT_CONFIG.API_PORT}/api`,
  WS_URL: `ws://${import.meta.env.VITE_API_HOST || DEFAULT_CONFIG.HOST}:${import.meta.env.VITE_API_PORT || DEFAULT_CONFIG.API_PORT}/ws`,
  IMAGE_BASE_URL: `http://${import.meta.env.VITE_API_HOST || DEFAULT_CONFIG.HOST}:${import.meta.env.VITE_API_PORT || DEFAULT_CONFIG.API_PORT}`,
  APP_TITLE: import.meta.env.VITE_APP_TITLE || `${DEFAULT_CONFIG.APP_TITLE}(测试)`
}

// 环境配置映射
const configs = {
  development,
  production,
  test
}

// 获取当前环境
const ENV = import.meta.env.MODE || 'development'

// 当前环境配置
const config = {
  ENV,
  ...configs[ENV],
  // 环境变量覆盖
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || configs[ENV].API_BASE_URL,
  WS_URL: import.meta.env.VITE_WS_URL || configs[ENV].WS_URL,
  IMAGE_BASE_URL: import.meta.env.VITE_IMAGE_BASE_URL || configs[ENV].IMAGE_BASE_URL,
  APP_TITLE: import.meta.env.VITE_APP_TITLE || configs[ENV].APP_TITLE,

  // 默认头像路径
  DEFAULT_AVATAR: import.meta.env.VITE_DEFAULT_AVATAR || '/uploads/default-avatar.png',

  // 网络配置
  REQUEST_TIMEOUT: parseInt(import.meta.env.VITE_REQUEST_TIMEOUT) || DEFAULT_CONFIG.REQUEST_TIMEOUT,
  WS_RECONNECT_INTERVAL: parseInt(import.meta.env.VITE_WS_RECONNECT_INTERVAL) || DEFAULT_CONFIG.WS_RECONNECT_INTERVAL,
  WS_MAX_RECONNECT_ATTEMPTS: parseInt(import.meta.env.VITE_WS_MAX_RECONNECT_ATTEMPTS) || DEFAULT_CONFIG.WS_MAX_RECONNECT_ATTEMPTS,

  // 分页配置
  PAGE_SIZE: parseInt(import.meta.env.VITE_PAGE_SIZE) || DEFAULT_CONFIG.PAGE_SIZE,

  // 上传配置
  UPLOAD_CONFIG: {
    MAX_FILE_SIZE: parseInt(import.meta.env.VITE_MAX_FILE_SIZE) || DEFAULT_CONFIG.MAX_FILE_SIZE,
    MAX_AVATAR_SIZE: parseInt(import.meta.env.VITE_MAX_AVATAR_SIZE) || DEFAULT_CONFIG.MAX_AVATAR_SIZE,
    ALLOWED_IMAGE_TYPES: (import.meta.env.VITE_ALLOWED_IMAGE_TYPES || 'image/jpeg,image/jpg,image/png,image/gif').split(','),
    ALLOWED_AVATAR_TYPES: (import.meta.env.VITE_ALLOWED_AVATAR_TYPES || 'image/jpeg,image/jpg,image/png').split(','),
    MAX_PHOTOS_COUNT: parseInt(import.meta.env.VITE_MAX_PHOTOS_COUNT) || DEFAULT_CONFIG.MAX_PHOTOS_COUNT,
    MAX_POST_IMAGES: parseInt(import.meta.env.VITE_MAX_POST_IMAGES) || DEFAULT_CONFIG.MAX_POST_IMAGES
  }
}

// 打印当前配置（开发环境）
if (ENV === 'development') {
  console.log('🔧 应用配置:', config)
}

export default config

// 导出常用配置
export const {
  API_BASE_URL,
  WS_URL,
  IMAGE_BASE_URL,
  APP_TITLE,
  DEFAULT_AVATAR,
  REQUEST_TIMEOUT,
  WS_RECONNECT_INTERVAL,
  WS_MAX_RECONNECT_ATTEMPTS,
  PAGE_SIZE
} = config

/**
 * 获取完整的图片URL
 * @param {string} path - 图片路径，如 '/uploads/avatar.png' 或 'avatar.png'
 * @returns {string} 完整的图片URL
 */
export const getImageUrl = (path) => {
  // 如果没有路径，返回默认头像
  if (!path || path === '' || path === null || path === undefined) {
    const baseUrl = config.API_BASE_URL.replace('/api', '')
    return `${baseUrl}${config.DEFAULT_AVATAR}`
  }

  // 如果已经是完整的HTTP URL，直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path
  }

  // 如果是相对路径且以 /uploads/ 开头，拼接服务器地址
  if (path.startsWith('/uploads/')) {
    const baseUrl = config.API_BASE_URL.replace('/api', '')
    return `${baseUrl}${path}`
  }

  // 如果是相对路径但不以 /uploads/ 开头，添加 /uploads/ 前缀
  if (!path.startsWith('/')) {
    const baseUrl = config.API_BASE_URL.replace('/api', '')
    return `${baseUrl}/uploads/${path}`
  }

  // 其他情况使用 IMAGE_BASE_URL
  return `${config.IMAGE_BASE_URL}${path}`
}

/**
 * 获取默认头像URL
 * @returns {string} 默认头像的完整URL
 */
export const getDefaultAvatarUrl = () => {
  const baseUrl = config.API_BASE_URL.replace('/api', '')
  return `${baseUrl}${config.DEFAULT_AVATAR}`
}