/**
 * 应用配置
 */

// 开发环境配置
const development = {
  API_BASE_URL: 'http://localhost:3000/api',
  WS_URL: 'ws://localhost:3000/ws',
  IMAGE_BASE_URL: 'http://localhost:3000',
  APP_TITLE: '社交匹配应用'
}

// 生产环境配置
const production = {
  API_BASE_URL: '/api',
  WS_URL: `ws://${window.location.host}/ws`,
  IMAGE_BASE_URL: '',
  APP_TITLE: '社交匹配应用'
}

// 测试环境配置
const test = {
  API_BASE_URL: 'http://localhost:3000/api',
  WS_URL: 'ws://localhost:3000/ws',
  IMAGE_BASE_URL: 'http://localhost:3000',
  APP_TITLE: '社交匹配应用(测试)'
}

// 环境配置映射
const configs = {
  development,
  production,
  test
}

// 获取当前环境
const ENV = import.meta.env.MODE || 'development'

// 当前环境配置
const config = {
  ENV,
  ...configs[ENV],
  // 环境变量覆盖
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || configs[ENV].API_BASE_URL,
  WS_URL: import.meta.env.VITE_WS_URL || configs[ENV].WS_URL,
  IMAGE_BASE_URL: import.meta.env.VITE_IMAGE_BASE_URL || configs[ENV].IMAGE_BASE_URL,
  APP_TITLE: import.meta.env.VITE_APP_TITLE || configs[ENV].APP_TITLE,
  // 默认头像路径
  DEFAULT_AVATAR: '/uploads/default-avatar.png',
  // 分页配置
  PAGE_SIZE: 20,
  // 上传配置
  UPLOAD_CONFIG: {
    MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
    MAX_AVATAR_SIZE: 5 * 1024 * 1024, // 5MB
    ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
    ALLOWED_AVATAR_TYPES: ['image/jpeg', 'image/jpg', 'image/png'],
    MAX_PHOTOS_COUNT: 6,
    MAX_POST_IMAGES: 9
  }
}

// 打印当前配置（开发环境）
if (ENV === 'development') {
  console.log('🔧 应用配置:', config)
}

export default config

// 导出常用配置
export const { API_BASE_URL, WS_URL, IMAGE_BASE_URL, APP_TITLE, DEFAULT_AVATAR } = config

/**
 * 获取完整的图片URL
 * @param {string} path - 图片路径，如 '/uploads/avatar.png' 或 'avatar.png'
 * @returns {string} 完整的图片URL
 */
export const getImageUrl = (path) => {
  // 如果没有路径，返回默认头像
  if (!path || path === '' || path === null || path === undefined) {
    const baseUrl = config.API_BASE_URL.replace('/api', '')
    return `${baseUrl}${config.DEFAULT_AVATAR}`
  }

  // 如果已经是完整的HTTP URL，直接返回
  if (path.startsWith('http://') || path.startsWith('https://')) {
    return path
  }

  // 如果是相对路径且以 /uploads/ 开头，拼接服务器地址
  if (path.startsWith('/uploads/')) {
    const baseUrl = config.API_BASE_URL.replace('/api', '')
    return `${baseUrl}${path}`
  }

  // 如果是相对路径但不以 /uploads/ 开头，添加 /uploads/ 前缀
  if (!path.startsWith('/')) {
    const baseUrl = config.API_BASE_URL.replace('/api', '')
    return `${baseUrl}/uploads/${path}`
  }

  // 其他情况使用 IMAGE_BASE_URL
  return `${config.IMAGE_BASE_URL}${path}`
}

/**
 * 获取默认头像URL
 * @returns {string} 默认头像的完整URL
 */
export const getDefaultAvatarUrl = () => {
  const baseUrl = config.API_BASE_URL.replace('/api', '')
  return `${baseUrl}${config.DEFAULT_AVATAR}`
}