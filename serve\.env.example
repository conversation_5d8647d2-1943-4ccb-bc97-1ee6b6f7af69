# 后端环境变量配置示例
# 复制此文件为 .env

# ===== 服务器配置 =====
# 服务器端口
PORT=3000
# 运行环境 (development, production, test)
NODE_ENV=development
# 前端开发服务器端口（用于CORS配置）
DEV_PORT=5173

# ===== 安全配置 =====
# JWT密钥（生产环境必须更改）
JWT_SECRET=your-secret-key-here
# JWT过期时间
JWT_EXPIRES_IN=7d

# ===== CORS配置 =====
# 允许的跨域源（逗号分隔）
CORS_ORIGIN=http://localhost:5173,http://localhost:3000

# ===== 请求限制配置 =====
# 请求体大小限制
REQUEST_LIMIT=10mb
# 限流时间窗口（毫秒）
RATE_LIMIT_WINDOW_MS=180000
# 限流最大请求数
RATE_LIMIT_MAX=1000
# 限流提示消息
RATE_LIMIT_MESSAGE=请求过于频繁，请稍后再试

# ===== 文件上传配置 =====
# 默认头像路径
DEFAULT_AVATAR=/uploads/default-avatar.png
# 最大文件大小（字节）
MAX_FILE_SIZE=10485760
# 最大头像文件大小（字节）
MAX_AVATAR_SIZE=5242880
# 允许的图片类型（逗号分隔）
ALLOWED_IMAGE_TYPES=image/jpeg,image/jpg,image/png,image/gif
# 允许的头像类型（逗号分隔）
ALLOWED_AVATAR_TYPES=image/jpeg,image/jpg,image/png
# 最大照片数量
MAX_PHOTOS_COUNT=6
# 最大帖子图片数量
MAX_POST_IMAGES=9

# ===== 数据库配置（如果使用数据库）=====
# DATABASE_URL=mongodb://localhost:27017/social_app
# DATABASE_NAME=social_app

# ===== 日志配置 =====
# LOG_LEVEL=info
# LOG_FILE=logs/app.log

# ===== 邮件配置（如果需要）=====
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# ===== 第三方服务配置 =====
# REDIS_URL=redis://localhost:6379
# UPLOAD_PROVIDER=local
# CDN_URL=https://your-cdn.com
