const express = require('express')
const { readData, updateData, addData } = require('../utils/dataStore')
const RegionUtils = require('../utils/regionUtils')
const { upload, avatarUpload } = require('../config/multer')

const router = express.Router()

/**
 * 获取用户资料
 * GET /api/user/profile
 */
router.get('/profile', async (req, res) => {
  try {
    const users = await readData('users')
    const user = users.find(u => u.id == req.user.id)

    if (!user) {
      return res.status(404).json({
        code: 4001,
        message: '用户不存在'
      })
    }

    // 返回用户资料（不包含密码）
    const { password, ...userProfile } = user

    

    res.json({
      code: 0,
      data: userProfile
    })

  } catch (error) {
    console.error('获取用户资料失败:', error)
    res.status(500).json({
      code: 500,
      message: '获取用户资料失败'
    })
  }
})

/**
 * 更新用户资料
 * PUT /api/user/profile
 */
router.put('/profile', async (req, res) => {
  try {
    const { username, nickname, age, gender, provinceCode, cityCode, tags, bio } = req.body

    // 参数验证
    const allowedFields = ['username', 'nickname', 'age', 'gender', 'provinceCode', 'cityCode', 'tags', 'bio']
    const updates = {}

    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        updates[field] = req.body[field]
      }
    })

    // 处理地区信息
    if (provinceCode || cityCode) {
      updates.provinceCode = provinceCode || ''
      updates.cityCode = cityCode || ''
    }

    // 如果传了username但没有nickname，则将username作为nickname
    if (username && !nickname) {
      updates.nickname = username
    }

    // 如果更新了用户名，检查是否重复
    if (username) {
      const users = await readData('users')
      const existingUser = users.find(u => u.username === username && u.id != req.user.id)
      
      if (existingUser) {
        return res.status(400).json({
          code: 2001,
          message: '用户名已存在'
        })
      }
    }

    // 标记资料为已完善
    if (Object.keys(updates).length > 0) {
      updates.profileCompleted = true
    }

    // 更新用户资料
    const updatedUser = await updateData('users', req.user.id, updates)

    // 返回更新后的资料（不包含密码）
    const { password, ...userProfile } = updatedUser

    res.json({
      code: 0,
      message: '更新成功',
      data: userProfile
    })

  } catch (error) {
    console.error('更新用户资料失败:', error)
    res.status(500).json({
      code: 500,
      message: '更新用户资料失败'
    })
  }
})

/**
 * 上传头像
 * POST /api/user/avatar
 */
router.post('/avatar', (req, res, next) => {
  console.log('📸 头像上传请求开始:', {
    method: req.method,
    url: req.url,
    contentType: req.headers['content-type'],
    contentLength: req.headers['content-length'],
    hasBody: !!req.body,
    bodyKeys: req.body ? Object.keys(req.body) : []
  })

  avatarUpload.single('avatar')(req, res, (err) => {
    if (err) {
      console.error('❌ Multer错误:', err)
      return res.status(400).json({
        code: 2001,
        message: err.message || '文件上传失败'
      })
    }

    console.log('📸 Multer处理完成:', {
      hasFile: !!req.file,
      hasFiles: !!req.files,
      bodyKeys: req.body ? Object.keys(req.body) : [],
      fileInfo: req.file ? {
        fieldname: req.file.fieldname,
        originalname: req.file.originalname,
        mimetype: req.file.mimetype,
        size: req.file.size,
        filename: req.file.filename
      } : null
    })

    next()
  })
}, async (req, res) => {
  try {
    if (!req.file) {
      console.error('❌ 没有接收到文件')
      return res.status(400).json({
        code: 2001,
        message: '请选择头像文件'
      })
    }

    const avatarUrl = `/uploads/${req.file.filename}`

    // 更新用户头像
    await updateData('users', req.user.id, { avatar: avatarUrl })

    // 记录上传信息
    await addData('uploads', {
      userId: req.user.id,
      type: 'avatar',
      filename: req.file.filename,
      originalName: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      url: avatarUrl
    })

    res.json({
      code: 0,
      message: '头像上传成功',
      data: {
        url: avatarUrl,
        size: req.file.size,
        mimeType: req.file.mimetype
      }
    })

  } catch (error) {
    console.error('上传头像失败:', error)
    res.status(500).json({
      code: 500,
      message: '上传头像失败'
    })
  }
})

/**
 * 上传相册
 * POST /api/user/photos
 */
router.post('/photos', upload.array('photos', 6), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        code: 2001,
        message: '请选择照片文件'
      })
    }

    const photoUrls = []
    
    // 处理每个上传的文件
    for (const file of req.files) {
      const photoUrl = `/uploads/${file.filename}`
      photoUrls.push(photoUrl)

      // 记录上传信息
      await addData('uploads', {
        userId: req.user.id,
        type: 'photo',
        filename: file.filename,
        originalName: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        url: photoUrl
      })
    }

    // 更新用户相册
    const users = await readData('users')
    const user = users.find(u => u.id == req.user.id)
    const currentImages = user.images || []
    const newImages = [...currentImages, ...photoUrls]

    await updateData('users', req.user.id, { images: newImages })

    res.json({
      code: 0,
      message: '照片上传成功',
      data: {
        urls: photoUrls,
        total: req.files.length
      }
    })

  } catch (error) {
    console.error('上传相册失败:', error)
    res.status(500).json({
      code: 500,
      message: '上传相册失败'
    })
  }
})

/**
 * 设置头像
 * PUT /api/user/avatar
 */
router.put('/avatar', async (req, res) => {
  try {
    const { photoUrl } = req.body

    if (!photoUrl) {
      return res.status(400).json({
        code: 2001,
        message: '请提供照片URL'
      })
    }

    // 更新用户头像
    await updateData('users', req.user.id, { avatar: photoUrl })

    res.json({
      code: 0,
      message: '头像设置成功',
      data: {
        avatar: photoUrl
      }
    })

  } catch (error) {
    console.error('设置头像失败:', error)
    res.status(500).json({
      code: 500,
      message: '设置头像失败'
    })
  }
})

/**
 * 获取其他用户资料
 * GET /api/user/profile/:id
 */
router.get('/profile/:id', async (req, res) => {
  try {
    const userId = parseInt(req.params.id)
    const currentUserId = req.user.id

    if (!userId || isNaN(userId)) {
      return res.status(400).json({
        code: 2001,
        message: '无效的用户ID'
      })
    }

    const users = await readData('users')
    const user = users.find(u => u.id === userId)

    if (!user) {
      return res.status(404).json({
        code: 4001,
        message: '用户不存在'
      })
    }

    // 检查是否被屏蔽
    const blocks = await readData('blocks') || []
    const isBlocked = blocks.some(block => 
      (block.userId === currentUserId && block.targetUserId === userId) ||
      (block.userId === userId && block.targetUserId === currentUserId)
    )

    if (isBlocked) {
      return res.status(403).json({
        code: 4003,
        message: '无法查看该用户资料'
      })
    }

    // 返回用户资料（不包含密码等敏感信息）
    const userProfile = {
      id: user.id,
      username: user.username,
      nickname: user.nickname,
      age: user.age,
      gender: user.gender,
      provinceCode: user.provinceCode || '',
      cityCode: user.cityCode || '',
      region: RegionUtils.formatRegionString(user.provinceCode, user.cityCode) || '',
      tags: user.tags || [],
      image: user.avatar || '/uploads/default-avatar.png',
      bio: user.bio || '',
      images: user.images || []
    }

    res.json({
      code: 0,
      data: userProfile
    })

  } catch (error) {
    console.error('获取用户资料失败:', error)
    res.status(500).json({
      code: 500,
      message: '获取用户资料失败'
    })
  }
})

/**
 * 获取用户相册
 * GET /api/user/photos/:id
 */
router.get('/photos/:id', async (req, res) => {
  try {
    const userId = parseInt(req.params.id)

    if (!userId || isNaN(userId)) {
      return res.status(400).json({
        code: 2001,
        message: '无效的用户ID'
      })
    }

    const users = await readData('users')
    const user = users.find(u => u.id === userId)

    if (!user) {
      return res.status(404).json({
        code: 4001,
        message: '用户不存在'
      })
    }

    // 构建相册数据
    const photos = (user.images || []).map((url, index) => ({
      id: index + 1,
      url: url
    }))

    res.json({
      code: 0,
      data: photos
    })

  } catch (error) {
    console.error('获取用户相册失败:', error)
    res.status(500).json({
      code: 500,
      message: '获取用户相册失败'
    })
  }
})

/**
 * 删除照片
 * DELETE /api/user/photos/:photoId
 */
router.delete('/photos/:photoId', async (req, res) => {
  try {
    const { photoId } = req.params
    const userId = req.user.id

    if (!photoId) {
      return res.status(400).json({
        code: 2001,
        message: '缺少照片ID'
      })
    }

    const users = await readData('users')
    const user = users.find(u => u.id === userId)

    if (!user) {
      return res.status(404).json({
        code: 4001,
        message: '用户不存在'
      })
    }

    let images = user.images || []
    
    // 如果 photoId 是数字，按索引删除
    if (!isNaN(photoId)) {
      const index = parseInt(photoId) - 1
      if (index >= 0 && index < images.length) {
        const removedUrl = images[index]
        images.splice(index, 1)
        
        // 删除物理文件
        try {
          const filename = path.basename(removedUrl)
          const filePath = path.join(__dirname, '../uploads', filename)
          if (await fs.pathExists(filePath)) {
            await fs.remove(filePath)
          }
        } catch (error) {
          console.warn('删除文件失败:', error)
        }
      } else {
        return res.status(404).json({
          code: 4001,
          message: '照片不存在'
        })
      }
    } else {
      // 如果 photoId 是 URL，按 URL 删除
      const index = images.findIndex(url => url.includes(photoId) || url === photoId)
      if (index !== -1) {
        const removedUrl = images[index]
        images.splice(index, 1)
        
        // 删除物理文件
        try {
          const filename = path.basename(removedUrl)
          const filePath = path.join(__dirname, '../uploads', filename)
          if (await fs.pathExists(filePath)) {
            await fs.remove(filePath)
          }
        } catch (error) {
          console.warn('删除文件失败:', error)
        }
      } else {
        return res.status(404).json({
          code: 4001,
          message: '照片不存在'
        })
      }
    }

    // 更新用户数据
    await updateData('users', userId, { images })

    res.json({
      code: 0,
      message: '照片删除成功'
    })

  } catch (error) {
    console.error('删除照片失败:', error)
    res.status(500).json({
      code: 500,
      message: '删除照片失败'
    })
  }
})

/**
 * 举报用户
 * POST /api/user/report
 */
router.post('/report', async (req, res) => {
  try {
    const { targetUserId, reason } = req.body
    const reporterId = req.user.id

    if (!targetUserId || !reason) {
      return res.status(400).json({
        code: 2001,
        message: '缺少必要参数'
      })
    }

    // 检查目标用户是否存在
    const users = await readData('users')
    const targetUser = users.find(u => u.id == targetUserId)

    if (!targetUser) {
      return res.status(404).json({
        code: 4001,
        message: '目标用户不存在'
      })
    }

    // 检查是否重复举报
    const reports = await readData('reports') || []
    const existingReport = reports.find(report => 
      report.reporterId === reporterId && 
      report.targetUserId == targetUserId &&
      report.type === 'user'
    )

    if (existingReport) {
      return res.status(400).json({
        code: 2001,
        message: '您已经举报过该用户'
      })
    }

    // 创建举报记录
    await addData('reports', {
      reporterId,
      targetUserId: parseInt(targetUserId),
      type: 'user',
      reason,
      status: 'pending',
      reportTime: new Date().toISOString()
    })

    res.json({
      code: 0,
      message: '举报已提交，我们会尽快处理'
    })

  } catch (error) {
    console.error('举报用户失败:', error)
    res.status(500).json({
      code: 500,
      message: '举报用户失败'
    })
  }
})

/**
 * 屏蔽用户
 * POST /api/user/block
 */
router.post('/block', async (req, res) => {
  try {
    const { targetUserId } = req.body
    const userId = req.user.id

    if (!targetUserId) {
      return res.status(400).json({
        code: 2001,
        message: '缺少目标用户ID'
      })
    }

    if (targetUserId == userId) {
      return res.status(400).json({
        code: 2001,
        message: '不能屏蔽自己'
      })
    }

    // 检查目标用户是否存在
    const users = await readData('users')
    const targetUser = users.find(u => u.id == targetUserId)

    if (!targetUser) {
      return res.status(404).json({
        code: 4001,
        message: '目标用户不存在'
      })
    }

    // 检查是否已经屏蔽
    const blocks = await readData('blocks') || []
    const existingBlock = blocks.find(block => 
      block.userId === userId && block.targetUserId == targetUserId
    )

    if (existingBlock) {
      return res.status(400).json({
        code: 2001,
        message: '已经屏蔽该用户'
      })
    }

    // 创建屏蔽记录
    await addData('blocks', {
      userId,
      targetUserId: parseInt(targetUserId),
      blockTime: new Date().toISOString()
    })

    res.json({
      code: 0,
      message: '已屏蔽该用户'
    })

  } catch (error) {
    console.error('屏蔽用户失败:', error)
    res.status(500).json({
      code: 500,
      message: '屏蔽用户失败'
    })
  }
})

/**
 * 获取用户的帖子列表
 * GET /api/user/:userId/posts
 */
router.get('/:userId/posts', async (req, res) => {
  try {
    const targetUserId = parseInt(req.params.userId)
    const currentUserId = req.user.id
    const { page = 1, limit = 10 } = req.query

    if (!targetUserId || isNaN(targetUserId)) {
      return res.status(400).json({
        code: 2001,
        message: '无效的用户ID'
      })
    }

    // 检查目标用户是否存在
    const users = await readData('users')
    const targetUser = users.find(u => u.id === targetUserId)

    if (!targetUser) {
      return res.status(404).json({
        code: 4001,
        message: '用户不存在'
      })
    }

    // 检查是否被屏蔽
    const blocks = await readData('blocks') || []
    const isBlocked = blocks.some(block => 
      (block.userId === currentUserId && block.targetUserId === targetUserId) ||
      (block.userId === targetUserId && block.targetUserId === currentUserId)
    )

    if (isBlocked) {
      return res.status(403).json({
        code: 4003,
        message: '无法查看该用户的帖子'
      })
    }

    // 获取用户的帖子
    let posts = await readData('posts')
    posts = posts.filter(post => post.userId === targetUserId)

    // 如果不是查看自己的帖子，只显示公开的帖子
    if (currentUserId !== targetUserId) {
      posts = posts.filter(post => post.privacy !== 'private')
    }

    // 获取点赞信息
    const likes = await readData('likes')
    
    // 获取评论信息
    const comments = await readData('comments')

    // 丰富帖子信息
    const enrichedPosts = posts.map(post => {
      // 统计点赞数
      const postLikes = likes.filter(like => 
        like.targetType === 'post' && 
        like.targetId == post.id && 
        like.action === 'like'
      )
      
      // 检查当前用户是否点赞
      const isLiked = postLikes.some(like => like.userId == currentUserId)
      
      // 统计评论数
      const postComments = comments.filter(comment => comment.postId == post.id)
      
      return {
        id: post.id,
        userId: post.userId,
        username: targetUser.username,
        avatar: targetUser.avatar || '/uploads/default-avatar.png',
        content: post.content,
        images: post.images || [],
        likeCount: postLikes.length,
        commentCount: postComments.length,
        shareCount: post.shares || 0,
        isLiked,
        isPrivate: post.privacy === 'private',
        isMine: currentUserId === targetUserId,
        createTime: post.createdAt
      }
    })

    // 按创建时间倒序排列
    enrichedPosts.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))

    // 简单分页实现
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + parseInt(limit)
    const paginatedPosts = enrichedPosts.slice(startIndex, endIndex)

    res.json({
      code: 0,
      data: paginatedPosts,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: enrichedPosts.length,
        totalPages: Math.ceil(enrichedPosts.length / limit)
      }
    })

  } catch (error) {
    console.error('获取用户帖子失败:', error)
    res.status(500).json({
      code: 500,
      message: '获取用户帖子失败'
    })
  }
})

/**
 * 记录访问
 * POST /api/user/visit
 */
router.post('/visit', async (req, res) => {
  try {
    const { targetUserId } = req.body
    const visitorId = req.user.id

    if (!targetUserId) {
      return res.status(400).json({
        code: 2001,
        message: '缺少目标用户ID'
      })
    }

    // 不能访问自己
    if (targetUserId == visitorId) {
      return res.json({
        code: 0,
        message: '访问记录已更新'
      })
    }

    // 检查目标用户是否存在
    const users = await readData('users')
    const targetUser = users.find(u => u.id == targetUserId)

    if (!targetUser) {
      return res.status(404).json({
        code: 4001,
        message: '用户不存在'
      })
    }

    // 检查是否被屏蔽
    const blocks = await readData('blocks') || []
    const isBlocked = blocks.some(block => 
      (block.userId === visitorId && block.targetUserId == targetUserId) ||
      (block.userId == targetUserId && block.targetUserId === visitorId)
    )

    if (isBlocked) {
      return res.json({
        code: 0,
        message: '访问记录已更新'
      })
    }

    // 记录访问
    const visitors = await readData('visitors') || []
    
    // 检查是否已经有访问记录
    const existingVisit = visitors.find(visit => 
      visit.visitorId === visitorId && visit.targetUserId == targetUserId
    )

    if (existingVisit) {
      // 更新访问时间
      existingVisit.visitTime = new Date().toISOString()
      existingVisit.visitCount = (existingVisit.visitCount || 1) + 1
      
      // 更新数据
      const updatedVisitors = visitors.map(visit => 
        visit.visitorId === visitorId && visit.targetUserId == targetUserId 
          ? existingVisit 
          : visit
      )
      
      const fs = require('fs-extra')
      const path = require('path')
      await fs.writeJson(path.join(__dirname, '../data/visitors.json'), updatedVisitors, { spaces: 2 })
    } else {
      // 新增访问记录
      await addData('visitors', {
        visitorId,
        targetUserId: parseInt(targetUserId),
        visitTime: new Date().toISOString(),
        visitCount: 1
      })
    }

    res.json({
      code: 0,
      message: '访问记录已更新'
    })

  } catch (error) {
    console.error('记录访问失败:', error)
    res.status(500).json({
      code: 500,
      message: '记录访问失败'
    })
  }
})

/**
 * 获取访客列表
 * GET /api/user/visitors
 */
router.get('/visitors', async (req, res) => {
  try {
    const userId = req.user.id
    const { page = 1, limit = 20 } = req.query

    // 获取访客记录
    const visitors = await readData('visitors') || []
    const userVisitors = visitors.filter(visit => visit.targetUserId === userId)

    // 按访问时间倒序排列
    userVisitors.sort((a, b) => new Date(b.visitTime) - new Date(a.visitTime))

    // 获取访客用户信息
    const users = await readData('users')
    const enrichedVisitors = userVisitors.map(visit => {
      const visitor = users.find(u => u.id === visit.visitorId)
      return visitor ? {
        id: visitor.id,
        name: visitor.username,
        nickname: visitor.nickname,
        avatar: visitor.avatar || '/uploads/default-avatar.png',
        age: visitor.age,
        provinceCode: visitor.provinceCode || '',
        cityCode: visitor.cityCode || '',
        region: RegionUtils.formatRegionString(visitor.provinceCode, visitor.cityCode) || '',
        visitTime: visit.visitTime,
        visitCount: visit.visitCount || 1
      } : null
    }).filter(Boolean) // 过滤掉null值

    // 分页
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + parseInt(limit)
    const paginatedVisitors = enrichedVisitors.slice(startIndex, endIndex)

    res.json({
      code: 0,
      data: paginatedVisitors,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: enrichedVisitors.length,
        totalPages: Math.ceil(enrichedVisitors.length / limit)
      }
    })

  } catch (error) {
    console.error('获取访客列表失败:', error)
    res.status(500).json({
      code: 500,
      message: '获取访客列表失败'
    })
  }
})

/**
 * 获取用户统计数据
 * GET /api/user/stats
 */
router.get('/stats', async (req, res) => {
  try {
    const userId = req.user.id

    // 获取访客数量和总访问次数
    const visitors = await readData('visitors') || []
    const userVisitors = visitors.filter(visit => visit.targetUserId === userId)
    
    // 统计不同访客数量
    const visitorCount = userVisitors.length
    
    // 统计总访问次数（所有访客的visitCount之和）
    const totalVisitCount = userVisitors.reduce((total, visit) => {
      return total + (visit.visitCount || 1)
    }, 0)

    // 获取点赞数量
    const likes = await readData('likes') || []
    const likeCount = likes.filter(like => 
      like.targetUserId == userId && 
      like.action === 'like'
    ).length

    // 获取匹配数量
    const matches = await readData('matches') || []
    const matchCount = matches.filter(match => 
      match.user1Id == userId || match.user2Id == userId
    ).length

    res.json({
      code: 0,
      data: {
        visitors: visitorCount,      // 不同访客数量
        totalVisits: totalVisitCount, // 总访问次数
        likes: likeCount,
        matches: matchCount
      }
    })

  } catch (error) {
    console.error('获取统计数据失败:', error)
    res.status(500).json({
      code: 500,
      message: '获取统计数据失败'
    })
  }
})

/**
 * 获取省市数据
 * GET /api/user/regions
 */
router.get('/regions', async (req, res) => {
  try {
    const { province } = req.query

    if (province) {
      // 如果指定了省份，返回该省份的城市列表
      const cities = RegionUtils.getCities(province)
      res.json({
        code: 0,
        data: cities
      })
    } else {
      // 返回省份和所有城市数据
      const provinces = RegionUtils.getProvinces()
      const cities = RegionUtils.getAllCities()

    res.json({
      code: 0,
      data: {
        provinces,
        cities
      }
    })
    }
  } catch (error) {
    console.error('获取地区数据失败:', error)
    res.status(500).json({
      code: 500,
      message: '获取地区数据失败'
    })
  }
})

module.exports = router 