<template>
  <div class="auth-container">
    <div class="background-shapes">
      <div class="shape shape-1" />
      <div class="shape shape-2" />
      <div class="shape shape-3" />
    </div>

    <div class="auth-card">
      <div class="auth-header">
        <div class="logo">
          <svg
            width="60"
            height="60"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#ff5864"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path
              d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"
            />
          </svg>
        </div>
        <h2>用户注册</h2>
        <p>创建账号，开始使用全部功能</p>
      </div>

      <div class="form">
        <div class="form-item">
          <div class="input-wrapper" :class="{ 'error': !fieldValidations.username.isValid && formState.touched.username }">
            <svg
              class="input-icon"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
              <circle cx="12" cy="7" r="4" />
            </svg>
            <input
              id="username"
              v-model="form.username"
              type="text"
              placeholder="请输入用户名"
              maxlength="20"
              autocomplete="username"
              :disabled="formState.isSubmitting"
              @blur="handleFieldBlur('username')"
              @focus="handleFieldFocus('username')"
            />
            <div v-if="fieldValidations.username.isValid && form.username" class="input-success">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#2ed573" stroke-width="2">
                <path d="M20 6L9 17l-5-5"/>
              </svg>
            </div>
          </div>
          <div v-if="!fieldValidations.username.isValid && formState.touched.username" class="error-message">
            {{ fieldValidations.username.message }}
          </div>
          <div v-else-if="form.username" class="help-text">
            用户名长度3-20位，只能包含字母、数字和下划线
          </div>
        </div>

        <div class="form-item">
          <div class="input-wrapper" :class="{ 'error': !fieldValidations.password.isValid && formState.touched.password }">
            <svg
              class="input-icon"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
              <path d="M7 11V7a5 5 0 0 1 10 0v4" />
            </svg>
            <input
              id="password"
              v-model="form.password"
              :type="formState.showPassword ? 'text' : 'password'"
              placeholder="请输入密码"
              maxlength="50"
              autocomplete="new-password"
              :disabled="formState.isSubmitting"
              @blur="handleFieldBlur('password')"
              @focus="handleFieldFocus('password')"
            />
            <button
              type="button"
              class="password-toggle"
              @click="formState.showPassword = !formState.showPassword"
              :disabled="formState.isSubmitting"
            >
              <svg v-if="formState.showPassword" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                <line x1="1" y1="1" x2="23" y2="23"/>
              </svg>
              <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                <circle cx="12" cy="12" r="3"/>
              </svg>
            </button>
          </div>
          <div v-if="!fieldValidations.password.isValid && formState.touched.password" class="error-message">
            {{ fieldValidations.password.message }}
          </div>
          <div v-else-if="form.password" class="password-strength">
            <div class="strength-bar">
              <div class="strength-fill" :style="{ width: (passwordStrength.level * 25) + '%', backgroundColor: passwordStrength.color }"></div>
            </div>
            <span class="strength-text" :style="{ color: passwordStrength.color }">
              密码强度：{{ passwordStrength.text }}
            </span>
          </div>
        </div>

        <div class="form-item">
          <div class="input-wrapper" :class="{ 'error': !fieldValidations.confirmPassword.isValid && formState.touched.confirmPassword }">
            <svg
              class="input-icon"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
              <path d="M7 11V7a5 5 0 0 1 10 0v4" />
            </svg>
            <input
              id="confirmPassword"
              v-model="form.confirmPassword"
              :type="formState.showConfirmPassword ? 'text' : 'password'"
              placeholder="请再次输入密码"
              maxlength="50"
              autocomplete="new-password"
              :disabled="formState.isSubmitting"
              @blur="handleFieldBlur('confirmPassword')"
              @focus="handleFieldFocus('confirmPassword')"
            />
            <button
              type="button"
              class="password-toggle"
              @click="formState.showConfirmPassword = !formState.showConfirmPassword"
              :disabled="formState.isSubmitting"
            >
              <svg v-if="formState.showConfirmPassword" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                <line x1="1" y1="1" x2="23" y2="23"/>
              </svg>
              <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                <circle cx="12" cy="12" r="3"/>
              </svg>
            </button>
            <div v-if="fieldValidations.confirmPassword.isValid && form.confirmPassword" class="input-success">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="#2ed573" stroke-width="2">
                <path d="M20 6L9 17l-5-5"/>
              </svg>
            </div>
          </div>
          <div v-if="!fieldValidations.confirmPassword.isValid && formState.touched.confirmPassword" class="error-message">
            {{ fieldValidations.confirmPassword.message }}
          </div>
        </div>

        <div class="form-actions">
          <button
            class="btn btn-primary"
            :class="{ 'loading': formState.isSubmitting, 'disabled': !isFormValid || formState.isSubmitting }"
            :disabled="!isFormValid || formState.isSubmitting"
            @click="handleRegister"
          >
            <span v-if="formState.isSubmitting" class="btn-loading">
              <svg class="loading-spinner" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M21 12a9 9 0 11-6.219-8.56"/>
              </svg>
              注册中...
            </span>
            <span v-else class="btn-content">
              <span class="btn-text">注 册</span>
              <span class="btn-icon">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="M5 12h14" />
                  <path d="m12 5 7 7-7 7" />
                </svg>
              </span>
            </span>
          </button>
        </div>

        <div class="form-footer">
          <p>
            已有账号？
            <router-link to="/login">去登录</router-link>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, inject, computed, reactive, watch, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '../stores/userStore'
import { authService } from '../services/authService'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const toast = inject('toast')

// 表单数据
const form = ref({
  username: '',
  password: '',
  confirmPassword: ''
})

// 表单状态
const formState = reactive({
  isSubmitting: false,
  showPassword: false,
  showConfirmPassword: false,
  touched: {
    username: false,
    password: false,
    confirmPassword: false
  }
})

// 验证规则
const validationRules = {
  username: {
    required: true,
    minLength: 3,
    maxLength: 20,
    pattern: /^[a-zA-Z0-9_]+$/,
    message: '用户名只能包含字母、数字和下划线，长度3-20位'
  },
  password: {
    required: true,
    minLength: 8,
    maxLength: 50,
    patterns: {
      hasLowerCase: /[a-z]/,
      hasUpperCase: /[A-Z]/,
      hasNumber: /\d/,
      hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/
    }
  }
}

// 验证函数
const validateField = (field, value) => {
  const rules = validationRules[field]
  if (!rules) return { isValid: true, message: '' }

  // 必填验证
  if (rules.required && (!value || value.trim() === '')) {
    return { isValid: false, message: `${getFieldLabel(field)}不能为空` }
  }

  // 长度验证
  if (value && rules.minLength && value.length < rules.minLength) {
    return { isValid: false, message: `${getFieldLabel(field)}长度至少${rules.minLength}位` }
  }

  if (value && rules.maxLength && value.length > rules.maxLength) {
    return { isValid: false, message: `${getFieldLabel(field)}长度不能超过${rules.maxLength}位` }
  }

  // 格式验证
  if (value && rules.pattern && !rules.pattern.test(value)) {
    return { isValid: false, message: rules.message || `${getFieldLabel(field)}格式不正确` }
  }

  return { isValid: true, message: '' }
}

const validatePassword = (password) => {
  if (!password) return { isValid: false, message: '密码不能为空' }

  const rules = validationRules.password
  const checks = {
    length: password.length >= rules.minLength && password.length <= rules.maxLength,
    hasLowerCase: rules.patterns.hasLowerCase.test(password),
    hasUpperCase: rules.patterns.hasUpperCase.test(password),
    hasNumber: rules.patterns.hasNumber.test(password),
    hasSpecialChar: rules.patterns.hasSpecialChar.test(password)
  }

  if (!checks.length) {
    return { isValid: false, message: `密码长度必须在${rules.minLength}-${rules.maxLength}位之间` }
  }

  const strengthCount = Object.values(checks).filter(Boolean).length - 1 // 排除长度检查
  if (strengthCount < 2) {
    return {
      isValid: false,
      message: '密码强度不足，至少包含大写字母、小写字母、数字、特殊字符中的两种'
    }
  }

  return { isValid: true, message: '', strength: strengthCount }
}

const validateConfirmPassword = (password, confirmPassword) => {
  if (!confirmPassword) {
    return { isValid: false, message: '请确认密码' }
  }

  if (password !== confirmPassword) {
    return { isValid: false, message: '两次输入的密码不一致' }
  }

  return { isValid: true, message: '' }
}

const getFieldLabel = (field) => {
  const labels = {
    username: '用户名',
    password: '密码',
    confirmPassword: '确认密码'
  }
  return labels[field] || field
}

// 计算属性 - 实时验证
const fieldValidations = computed(() => ({
  username: formState.touched.username ? validateField('username', form.value.username) : { isValid: true, message: '' },
  password: formState.touched.password ? validatePassword(form.value.password) : { isValid: true, message: '' },
  confirmPassword: formState.touched.confirmPassword ?
    validateConfirmPassword(form.value.password, form.value.confirmPassword) :
    { isValid: true, message: '' }
}))

const isFormValid = computed(() => {
  return Object.values(fieldValidations.value).every(validation => validation.isValid) &&
         form.value.username && form.value.password && form.value.confirmPassword
})

const passwordStrength = computed(() => {
  if (!form.value.password) return { level: 0, text: '', color: '' }

  const validation = validatePassword(form.value.password)
  if (!validation.isValid) return { level: 0, text: '密码强度不足', color: '#ff4757' }

  const strength = validation.strength || 0
  const levels = [
    { level: 0, text: '弱', color: '#ff4757' },
    { level: 1, text: '弱', color: '#ff4757' },
    { level: 2, text: '中', color: '#ffa502' },
    { level: 3, text: '强', color: '#2ed573' },
    { level: 4, text: '很强', color: '#1e90ff' }
  ]

  return levels[Math.min(strength, 4)]
})

// 事件处理
const handleFieldBlur = (field) => {
  formState.touched[field] = true
}

const handleFieldFocus = (field) => {
  // 可以在这里添加焦点处理逻辑
}

const sanitizeInput = (value, field) => {
  if (field === 'username') {
    // 移除特殊字符，只保留字母数字下划线
    return value.replace(/[^a-zA-Z0-9_]/g, '')
  }
  return value
}

const handleRegister = async () => {
  // 防止重复提交
  if (formState.isSubmitting) {
    console.warn('注册正在进行中，请勿重复提交')
    return
  }

  // 标记所有字段为已触摸，显示验证错误
  Object.keys(formState.touched).forEach(key => {
    formState.touched[key] = true
  })

  // 验证表单
  if (!isFormValid.value) {
    const firstError = Object.values(fieldValidations.value).find(v => !v.isValid)
    if (firstError) {
      toast.error(firstError.message)
    }
    return
  }

  formState.isSubmitting = true

  try {
    // 准备注册数据
    const registerData = {
      username: form.value.username.trim(),
      password: form.value.password,
      confirmPassword: form.value.confirmPassword
    }

    console.log('开始注册用户:', registerData.username)

    // 调用注册API
    const response = await authService.register(registerData)

    if (!response || !response.token || !response.userInfo) {
      throw new Error('注册响应数据不完整')
    }

    // 保存token和用户信息
    userStore.setToken(response.token)
    userStore.setUserInfo(response.userInfo)

    toast.success('注册成功！欢迎加入 💕')

    console.log('注册成功，用户信息:', response.userInfo)

    // 延迟跳转，让用户看到提示
    setTimeout(() => {
      try {
        // 根据用户资料完成状态决定跳转路径
        const targetRoute = !response.userInfo.profileCompleted ? '/complete-profile' : '/'
        console.log('跳转到:', targetRoute)
        router.push(targetRoute)
      } catch (routerError) {
        console.error('路由跳转失败:', routerError)
        // 如果路由跳转失败，尝试跳转到首页
        router.push('/')
      }
    }, 1200)

  } catch (error) {
    console.error('注册失败:', error)

    // 根据错误类型提供不同的提示
    let errorMessage = '注册失败，请稍后再试'

    if (error.message) {
      if (error.message.includes('用户名已存在')) {
        errorMessage = '用户名已存在，请选择其他用户名'
      } else if (error.message.includes('密码')) {
        errorMessage = '密码格式不符合要求'
      } else if (error.message.includes('网络')) {
        errorMessage = '网络连接失败，请检查网络后重试'
      } else {
        errorMessage = error.message
      }
    }

    toast.error(errorMessage)
  } finally {
    formState.isSubmitting = false
  }
}

// 监听器
watch(() => form.value.username, (newValue) => {
  form.value.username = sanitizeInput(newValue, 'username')
})

// 生命周期
onMounted(() => {
  console.log('Register组件已挂载')
  // 如果用户已登录，重定向到首页
  if (userStore.isLogin) {
    console.log('用户已登录，重定向到首页')
    router.push('/')
  }
})

onUnmounted(() => {
  console.log('Register组件即将卸载')
  // 清理状态
  formState.isSubmitting = false
})
</script>

<style scoped>
/* 表单验证样式 */
.input-wrapper {
  position: relative;
  transition: all 0.3s ease;
}

.input-wrapper.error {
  border-color: #ff4757;
}

.input-wrapper.error .input-icon {
  color: #ff4757;
}

.input-success {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
}

.password-toggle {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.password-toggle:hover {
  color: var(--color-primary);
  background-color: rgba(255, 88, 100, 0.1);
}

.password-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.error-message {
  color: #ff4757;
  font-size: 0.85rem;
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.error-message::before {
  content: "⚠";
  font-size: 0.9rem;
}

.help-text {
  color: #666;
  font-size: 0.85rem;
  margin-top: 6px;
}

/* 密码强度指示器 */
.password-strength {
  margin-top: 8px;
}

.strength-bar {
  width: 100%;
  height: 4px;
  background-color: #e1e8ed;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 6px;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-text {
  font-size: 0.85rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

/* 按钮状态 */
.btn.loading {
  pointer-events: none;
}

.btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-loading {
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 输入框焦点状态优化 */
.input-wrapper:focus-within {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(255, 88, 100, 0.1);
}

.input-wrapper:focus-within .input-icon {
  color: var(--color-primary);
}

/* 表单项间距优化 */
.form-item {
  margin-bottom: 24px;
}

.form-item:last-of-type {
  margin-bottom: 32px;
}

/* 响应式优化 */
@media (max-width: 480px) {
  .error-message,
  .help-text,
  .strength-text {
    font-size: 0.8rem;
  }

  .password-toggle {
    right: 8px;
  }

  .input-success {
    right: 8px;
  }
}

.form-footer {
  margin-top: 2rem;
  text-align: center;
  color: var(--font-color-secondary);
  font-size: 0.95rem;
}

.form-footer a {
  color: var(--color-primary);
  font-weight: 600;
  transition: color 0.2s ease;
}

.form-footer a:hover {
  color: #e63946;
}
</style>
