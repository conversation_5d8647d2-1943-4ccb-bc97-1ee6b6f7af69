<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>头像测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .avatar-test {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 10px 0;
        }
        .avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #ddd;
        }
        .avatar-info {
            flex: 1;
        }
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .url-display {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            font-size: 12px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>头像地址测试页面</h1>
        
        <div class="test-section">
            <h3>1. 默认头像测试</h3>
            <div class="avatar-test">
                <img class="avatar" src="http://localhost:3000/uploads/default-avatar.png" alt="默认头像" onload="updateStatus(this, 'success')" onerror="updateStatus(this, 'error')">
                <div class="avatar-info">
                    <div><strong>默认头像</strong></div>
                    <div class="url-display">http://localhost:3000/uploads/default-avatar.png</div>
                    <div class="status" id="status-default">加载中...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>2. 用户头像测试</h3>
            <div class="avatar-test">
                <img class="avatar" src="http://localhost:3000/uploads/photos-1750917844259-619424313.jpg" alt="用户头像1" onload="updateStatus(this, 'success')" onerror="updateStatus(this, 'error')">
                <div class="avatar-info">
                    <div><strong>用户1头像</strong></div>
                    <div class="url-display">http://localhost:3000/uploads/photos-1750917844259-619424313.jpg</div>
                    <div class="status" id="status-user1">加载中...</div>
                </div>
            </div>
            
            <div class="avatar-test">
                <img class="avatar" src="http://localhost:3000/uploads/photos-1750837872752-186775365.png" alt="用户头像2" onload="updateStatus(this, 'success')" onerror="updateStatus(this, 'error')">
                <div class="avatar-info">
                    <div><strong>用户2头像</strong></div>
                    <div class="url-display">http://localhost:3000/uploads/photos-1750837872752-186775365.png</div>
                    <div class="status" id="status-user2">加载中...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>3. 错误路径测试</h3>
            <div class="avatar-test">
                <img class="avatar" src="http://localhost:3000/uploads/non-existent.jpg" alt="不存在的图片" onload="updateStatus(this, 'success')" onerror="updateStatus(this, 'error')">
                <div class="avatar-info">
                    <div><strong>不存在的图片</strong></div>
                    <div class="url-display">http://localhost:3000/uploads/non-existent.jpg</div>
                    <div class="status" id="status-error">加载中...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>4. API测试</h3>
            <button onclick="testAPI()">测试获取用户数据</button>
            <div id="api-result" style="margin-top: 10px;"></div>
        </div>
    </div>

    <script>
        function updateStatus(img, status) {
            const statusElement = img.parentElement.querySelector('.status');
            if (status === 'success') {
                statusElement.textContent = '✅ 加载成功';
                statusElement.className = 'status success';
            } else {
                statusElement.textContent = '❌ 加载失败';
                statusElement.className = 'status error';
                // 设置默认头像
                img.src = 'http://localhost:3000/uploads/default-avatar.png';
            }
        }

        async function testAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '正在测试API...';
            
            try {
                // 测试健康检查
                const healthResponse = await fetch('http://localhost:3000/health');
                const healthData = await healthResponse.json();
                
                resultDiv.innerHTML = `
                    <h4>API测试结果:</h4>
                    <div class="status success">✅ 服务器连接正常</div>
                    <div class="url-display">${JSON.stringify(healthData, null, 2)}</div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h4>API测试结果:</h4>
                    <div class="status error">❌ 服务器连接失败: ${error.message}</div>
                `;
            }
        }

        // 页面加载完成后自动测试API
        window.onload = function() {
            setTimeout(testAPI, 1000);
        };
    </script>
</body>
</html>
