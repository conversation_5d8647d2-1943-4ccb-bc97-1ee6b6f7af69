<template>
  <page-layout title="推荐">
    <!-- 筛选组件 -->
    <filter-bar @filter="handleFilter" />

    <div class="card-stack">
      <!-- 加载中显示骨架屏 -->
      <skeleton-card v-if="loading" />

      <!-- 重试中状态 -->
      <div v-else-if="isRetrying" class="retry-state">
        <div class="retry-icon">🔄</div>
        <p>正在重试加载...</p>
        <small>第 {{ retryCount }} 次尝试</small>
      </div>

      <!-- 卡片列表 -->
      <div v-else-if="displayCards.length > 0" class="cards-container">
        <card-item
          v-for="(card, index) in displayCards"
          :key="`card-${card.id}-${index}`"
          :id="card.id"
          :index="index + 1"
          :username="card.nickname || card.username || '未知用户'"
          :gender="card.gender"
          :age="card.age"
          :region="card.region"
          :tags="Array.isArray(card.tags) ? card.tags : []"
          :avatar="card.avatar"
          :image="card.image"
          :images="Array.isArray(card.images) ? card.images : []"
          :enable-swipe="!isDisabled"
          @swipe-left="() => handleSwipe(card.id, 'dislike')"
          @swipe-right="() => handleSwipe(card.id, 'like')"
        />
      </div>

      <!-- 空状态 -->
      <empty-state
        v-else
        text="没有符合条件的用户"
        description="请调整筛选条件或稍后再试"
        class="centered-empty-state"
      />
    </div>

    <!-- 操作按钮 -->
    <div v-if="hasCards" class="action-buttons">
      <action-button type="dislike" icon="✕" :disabled="isDisabled" @click="() => handleButtonClick('dislike')" />
      <action-button type="like" icon="♥" :disabled="isDisabled" @click="() => handleButtonClick('like')" />
    </div>
  </page-layout>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, inject, nextTick } from 'vue'
import PageLayout from '../components/PageLayout.vue'
import CardItem from '../components/CardItem.vue'
import FilterBar from '../components/FilterBar.vue'
import EmptyState from '../components/EmptyState.vue'
import ActionButton from '../components/ActionButton.vue'
import SkeletonCard from '../components/SkeletonCard.vue'
import { matchService } from '../services/matchService'

// 常量配置
const MAX_DISPLAY_CARDS = 3
const MIN_CARDS_THRESHOLD = 2
const RETRY_DELAY = 1000
const MAX_RETRY_ATTEMPTS = 3

// 响应式状态
const loading = ref(true)
const cardData = ref([])
const filters = ref({})
const retryCount = ref(0)
const isRetrying = ref(false)
const toast = inject('toast')

// 数据验证函数
const validateCard = (card) => {
  return card &&
         typeof card.id !== 'undefined' &&
         card.nickname &&
         card.age &&
         card.gender
}

// 筛选器配置
const filterHandlers = {
  gender: (cards, value) => cards.filter(card =>
    validateCard(card) && card.gender === value
  ),
  age: (cards, [minAge, maxAge]) => cards.filter(card =>
    validateCard(card) && card.age >= minAge && card.age <= maxAge
  ),
  provinceCode: (cards, value) => cards.filter(card =>
    validateCard(card) && card.provinceCode === value
  ),
  cityCode: (cards, value) => cards.filter(card =>
    validateCard(card) && card.cityCode === value
  ),
  tags: (cards, tags) => cards.filter(card =>
    validateCard(card) &&
    Array.isArray(card.tags) &&
    tags.some(tag => card.tags.includes(tag))
  )
}

// 计算属性
const filteredCards = computed(() => {
  try {
    // 确保cardData是数组
    const cards = Array.isArray(cardData.value) ? cardData.value : []

    return Object.entries(filters.value).reduce((result, [key, value]) => {
      if (value && filterHandlers[key]) {
        return filterHandlers[key](result, value)
      }
      return result
    }, cards)
  } catch (error) {
    console.error('筛选卡片时出错:', error)
    return []
  }
})

const displayCards = computed(() => {
  const cards = filteredCards.value.slice(0, MAX_DISPLAY_CARDS)
  return cards.filter(validateCard) // 再次验证显示的卡片
})

const hasCards = computed(() =>
  displayCards.value.length > 0 && !loading.value
)

const isDisabled = computed(() =>
  loading.value || displayCards.value.length === 0 || isRetrying.value
)

const shouldLoadMore = computed(() =>
  cardData.value.length < MIN_CARDS_THRESHOLD && !loading.value
)

// 工具函数
const transformFilterParams = (filterParams) => {
  const apiParams = {}

  if (filterParams.gender) {
    apiParams.gender = filterParams.gender
  }

  if (filterParams.age && Array.isArray(filterParams.age)) {
    apiParams.age_min = filterParams.age[0]
    apiParams.age_max = filterParams.age[1]
  }

  if (filterParams.provinceCode) {
    apiParams.provinceCode = filterParams.provinceCode
  }

  if (filterParams.cityCode) {
    apiParams.cityCode = filterParams.cityCode
  }

  if (filterParams.tags?.length > 0) {
    apiParams.tags = filterParams.tags
  }

  return apiParams
}

const handleLoadError = (error, context = '操作') => {
  console.error(`${context}失败:`, error)
  const message = error.message || `${context}失败`
  toast?.error(message)

  // 如果是网络错误，可以尝试重试
  if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
    return true // 表示可以重试
  }

  return false
}

// 主要方法
const loadRecommendations = async (filterParams = {}, isRetry = false) => {
  try {
    if (!isRetry) {
      loading.value = true
      retryCount.value = 0
    } else {
      isRetrying.value = true
    }

    const apiParams = transformFilterParams(filterParams)
    const response = await matchService.getRecommendations(apiParams)

    // 验证响应数据
    const validCards = Array.isArray(response)
      ? response.filter(validateCard)
      : []

    cardData.value = validCards
    retryCount.value = 0 // 成功后重置重试计数

  } catch (error) {
    const canRetry = handleLoadError(error, '获取推荐用户')

    // 自动重试逻辑
    if (canRetry && retryCount.value < MAX_RETRY_ATTEMPTS) {
      retryCount.value++
      setTimeout(() => {
        loadRecommendations(filterParams, true)
      }, RETRY_DELAY * retryCount.value)
      return
    }

    cardData.value = []
  } finally {
    loading.value = false
    isRetrying.value = false
  }
}

const initializeData = async () => {
  try {
    await loadRecommendations()
  } catch (error) {
    console.error('初始化数据失败:', error)
  }
}

const handleFilter = async (filterData) => {
  try {
    // 验证筛选数据
    if (typeof filterData !== 'object' || filterData === null) {
      console.warn('无效的筛选数据:', filterData)
      return
    }

    filters.value = { ...filterData } // 创建副本避免引用问题
    await loadRecommendations(filterData)
  } catch (error) {
    handleLoadError(error, '应用筛选条件')
  }
}

const removeCard = (id) => {
  try {
    if (!id) {
      console.warn('removeCard: 无效的卡片ID')
      return
    }

    const index = cardData.value.findIndex(card => card.id === id)
    if (index !== -1) {
      cardData.value.splice(index, 1)

      // 使用nextTick确保DOM更新后再检查是否需要加载更多
      nextTick(() => {
        if (shouldLoadMore.value) {
          loadRecommendations(filters.value)
        }
      })
    }
  } catch (error) {
    console.error('移除卡片失败:', error)
  }
}

const handleSwipe = async (id, action) => {
  // 防止重复操作
  if (loading.value || isRetrying.value) {
    console.warn('操作进行中，请稍候')
    return
  }

  // 验证参数
  if (!id || !action) {
    console.error('handleSwipe: 无效的参数', { id, action })
    return
  }

  if (!['like', 'dislike'].includes(action)) {
    console.error('handleSwipe: 无效的操作类型', action)
    return
  }

  try {
    // 乐观更新：先移除卡片，提升用户体验
    const cardToRemove = cardData.value.find(card => card.id === id)
    if (!cardToRemove) {
      console.warn('未找到要操作的卡片:', id)
      return
    }

    removeCard(id)

    // 发送匹配操作到服务器
    const response = await matchService.performAction(id, action)

    // 如果匹配成功，显示提示并更新匹配列表
    if (response?.matched) {
      toast?.success('恭喜！你们匹配成功了 💕')

      // 通知messageStore更新匹配列表
      const { useMessageStore } = await import('../stores/messageStore')
      const messageStore = useMessageStore()
      await messageStore.loadMatches()

    } else if (action === 'like') {
      toast?.success('已发送喜欢 ❤️')
    }

  } catch (error) {
    // 如果操作失败，恢复卡片（回滚）
    console.error('匹配操作失败:', error)

    // 将卡片重新添加到原位置
    const cardToRestore = cardData.value.find(card => card.id === id)
    if (!cardToRestore && cardToRemove) {
      cardData.value.unshift(cardToRemove)
    }

    handleLoadError(error, '匹配操作')
  }
}

const handleButtonClick = (action) => {
  if (isDisabled.value) {
    console.warn('按钮被禁用，无法执行操作')
    return
  }

  const firstCard = displayCards.value[0]
  if (firstCard?.id) {
    handleSwipe(firstCard.id, action)
  } else {
    console.warn('没有可操作的卡片')
  }
}

// 清理函数
const cleanup = () => {
  // 清理定时器、取消请求等
  retryCount.value = 0
  isRetrying.value = false
}

// 生命周期
onMounted(() => {
  console.log('Home组件已挂载，开始初始化数据')
  initializeData()
})

onUnmounted(() => {
  console.log('Home组件即将卸载，执行清理')
  cleanup()
})
</script>

<style scoped>
.card-stack {
  position: relative;
  width: 100%;
  max-width: 360px;
  height: 480px;
  margin: 20px auto;
  perspective: 1200px;
  z-index: 1;
  display: flex;
  flex-direction: column;
}

.cards-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.centered-empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}

.retry-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
}

.retry-icon {
  font-size: 32px;
  margin-bottom: 12px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.retry-state p {
  margin: 8px 0 4px 0;
  font-size: 16px;
  font-weight: 500;
}

.retry-state small {
  font-size: 12px;
  color: #999;
}

.action-buttons {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  position: relative;
  z-index: 30;
  gap: 40px;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .card-stack {
    height: 450px;
    margin: 15px auto;
  }

  .action-buttons {
    margin-top: 25px;
    gap: 36px;
  }
}

@media (min-width: 768px) {
  .card-stack {
    max-width: 400px;
    height: 520px;
  }
}
</style>
