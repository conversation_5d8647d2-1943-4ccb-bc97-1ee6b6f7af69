<template>
  <page-layout title="推荐">
    <!-- 筛选组件 -->
    <filter-bar @filter="handleFilter" />

    <div class="card-stack">
      <!-- 加载中显示骨架屏 -->
      <skeleton-card v-if="loading" />

      <!-- 卡片列表 -->
      <div v-else class="cards-container">
        <card-item v-for="(card, index) in displayCards" :id="card.id" :key="card.id" :index="index + 1"
          :username="card.nickname || card.username" :gender="card.gender" :age="card.age" :region="card.region" :tags="card.tags"
          :avatar="card.avatar" :image="card.image" :images="card.images" :enable-swipe="true"
          @swipe-left="() => handleSwipe(card.id, 'dislike')" @swipe-right="() => handleSwipe(card.id, 'like')" />
      </div>

      <!-- 空状态 -->
      <empty-state v-if="displayCards.length === 0 && !loading" text="没有符合条件的用户" description="请调整筛选条件"
        class="centered-empty-state" />
    </div>

    <!-- 操作按钮 -->
    <div v-if="hasCards" class="action-buttons">
      <action-button type="dislike" icon="✕" :disabled="isDisabled" @click="() => handleButtonClick('dislike')" />
      <action-button type="like" icon="♥" :disabled="isDisabled" @click="() => handleButtonClick('like')" />
    </div>
  </page-layout>
</template>

<script setup>
import { ref, computed, onMounted, inject } from 'vue'
import PageLayout from '../components/PageLayout.vue'
import CardItem from '../components/CardItem.vue'
import FilterBar from '../components/FilterBar.vue'
import EmptyState from '../components/EmptyState.vue'
import ActionButton from '../components/ActionButton.vue'
import SkeletonCard from '../components/SkeletonCard.vue'
import { matchService } from '../services/matchService'

// 常量配置
const MAX_DISPLAY_CARDS = 3

// 响应式状态
const loading = ref(true)
const cardData = ref([])
const filters = ref({})
const toast = inject('toast')

// 筛选器配置
const filterHandlers = {
  gender: (cards, value) => cards.filter(card => card.gender === value),
  age: (cards, [minAge, maxAge]) => cards.filter(card => card.age >= minAge && card.age <= maxAge),
  provinceCode: (cards, value) => cards.filter(card => card.provinceCode === value),
  cityCode: (cards, value) => cards.filter(card => card.cityCode === value),
  tags: (cards, tags) => cards.filter(card => tags.some(tag => card.tags.includes(tag)))
}

// 计算属性
const filteredCards = computed(() => {
  return Object.entries(filters.value).reduce((result, [key, value]) => {
    if (value && filterHandlers[key]) {
      return filterHandlers[key](result, value)
    }
    return result
  }, cardData.value)
})

const displayCards = computed(() =>
  filteredCards.value.slice(0, MAX_DISPLAY_CARDS)
)

const hasCards = computed(() =>
  displayCards.value.length > 0 && !loading.value
)

const isDisabled = computed(() =>
  loading.value || displayCards.value.length === 0
)

// 方法
const loadRecommendations = async (filterParams = {}) => {
  try {
    loading.value = true
    
    // 转换筛选参数格式
    const apiParams = {}
    if (filterParams.gender) {
      apiParams.gender = filterParams.gender
    }
    if (filterParams.age) {
      apiParams.age_min = filterParams.age[0]
      apiParams.age_max = filterParams.age[1]
    }
    if (filterParams.provinceCode) {
      apiParams.provinceCode = filterParams.provinceCode
    }
    if (filterParams.cityCode) {
      apiParams.cityCode = filterParams.cityCode
    }
    if (filterParams.tags?.length > 0) {
      apiParams.tags = filterParams.tags
    }

    const response = await matchService.getRecommendations(apiParams)
    cardData.value = response || []
  } catch (error) {
    console.error('获取推荐用户失败:', error)
    toast?.error(error.message || '获取推荐用户失败')
    cardData.value = []
  } finally {
    loading.value = false
  }
}

const initializeData = async () => {
  await loadRecommendations()
}

const handleFilter = async (filterData) => {
  filters.value = filterData
  await loadRecommendations(filterData)
}

const removeCard = (id) => {
  const index = cardData.value.findIndex(card => card.id === id)
  if (index !== -1) {
    cardData.value.splice(index, 1)
  }
}

const handleSwipe = async (id, action) => {
  if (loading.value) return

  try {
    // 发送匹配操作到服务器
    const response = await matchService.performAction(id, action)
    
    // 如果匹配成功，显示提示
    if (response.matched) {
      toast?.success('恭喜！你们匹配成功了')
    }
    
    // 移除卡片
    removeCard(id)
    
    // 如果卡片数量不足，尝试加载更多
    if (cardData.value.length < 2) {
      await loadRecommendations(filters.value)
    }
  } catch (error) {
    console.error('操作失败:', error)
    toast?.error(error.message || '操作失败')
  }
}

const handleButtonClick = (action) => {
  if (isDisabled.value) return

  const firstCard = displayCards.value[0]
  if (firstCard) {
    handleSwipe(firstCard.id, action)
  }
}

// 生命周期
onMounted(initializeData)
</script>

<style scoped>
.card-stack {
  position: relative;
  width: 100%;
  max-width: 360px;
  height: 480px;
  margin: 20px auto;
  perspective: 1200px;
  z-index: 1;
  display: flex;
  flex-direction: column;
}

.cards-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.centered-empty-state {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
}

.action-buttons {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  position: relative;
  z-index: 30;
  gap: 40px;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .card-stack {
    height: 450px;
    margin: 15px auto;
  }

  .action-buttons {
    margin-top: 25px;
    gap: 36px;
  }
}

@media (min-width: 768px) {
  .card-stack {
    max-width: 400px;
    height: 520px;
  }
}
</style>
