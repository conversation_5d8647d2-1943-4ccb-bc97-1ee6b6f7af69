<template>
  <page-layout title="通知管理" :show-back="true" hide-tab-bar hide-safe-area>
    <template #header-right>
      <button class="confirm-btn" @click="saveSettings">
        确定
      </button>
    </template>
    <div class="notification-settings">
      <!-- 系统通知设置 -->
      <div class="settings-section">
        <h3 class="section-title">系统通知</h3>
        <div class="setting-item">
          <div class="setting-info">
            <div class="setting-name">新消息通知</div>
            <div class="setting-desc">收到新消息时推送通知</div>
          </div>
          <div class="setting-control">
            <label class="switch">
              <input 
                type="checkbox" 
                v-model="settings.newMessage"
                @change="updateSetting('newMessage', settings.newMessage)"
              >
              <span class="slider"></span>
            </label>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-info">
            <div class="setting-name">匹配通知</div>
            <div class="setting-desc">有新的匹配时通知我</div>
          </div>
          <div class="setting-control">
            <label class="switch">
              <input 
                type="checkbox" 
                v-model="settings.newMatch"
                @change="updateSetting('newMatch', settings.newMatch)"
              >
              <span class="slider"></span>
            </label>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-info">
            <div class="setting-name">点赞通知</div>
            <div class="setting-desc">有人点赞我的动态时通知</div>
          </div>
          <div class="setting-control">
            <label class="switch">
              <input 
                type="checkbox" 
                v-model="settings.newLike"
                @change="updateSetting('newLike', settings.newLike)"
              >
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </div>

      <!-- 声音设置 -->
      <div class="settings-section">
        <h3 class="section-title">声音设置</h3>
        <div class="setting-item">
          <div class="setting-info">
            <div class="setting-name">消息提示音</div>
            <div class="setting-desc">收到消息时播放提示音</div>
          </div>
          <div class="setting-control">
            <label class="switch">
              <input 
                type="checkbox" 
                v-model="settings.messageSound"
                @change="updateSetting('messageSound', settings.messageSound)"
              >
              <span class="slider"></span>
            </label>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-info">
            <div class="setting-name">震动反馈</div>
            <div class="setting-desc">接收通知时震动提醒</div>
          </div>
          <div class="setting-control">
            <label class="switch">
              <input 
                type="checkbox" 
                v-model="settings.vibration"
                @change="updateSetting('vibration', settings.vibration)"
              >
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </div>

      <!-- 隐私设置 -->
      <div class="settings-section">
        <h3 class="section-title">隐私设置</h3>
        <div class="setting-item">
          <div class="setting-info">
            <div class="setting-name">显示在线状态</div>
            <div class="setting-desc">让其他用户看到我的在线状态</div>
          </div>
          <div class="setting-control">
            <label class="switch">
              <input 
                type="checkbox" 
                v-model="settings.showOnlineStatus"
                @change="updateSetting('showOnlineStatus', settings.showOnlineStatus)"
              >
              <span class="slider"></span>
            </label>
          </div>
        </div>

        <div class="setting-item">
          <div class="setting-info">
            <div class="setting-name">读取回执</div>
            <div class="setting-desc">显示已读状态给对方</div>
          </div>
          <div class="setting-control">
            <label class="switch">
              <input 
                type="checkbox" 
                v-model="settings.readReceipt"
                @change="updateSetting('readReceipt', settings.readReceipt)"
              >
              <span class="slider"></span>
            </label>
          </div>
        </div>
      </div>

      <!-- 勿扰模式 -->
      <div class="settings-section">
        <h3 class="section-title">勿扰模式</h3>
        <div class="setting-item">
          <div class="setting-info">
            <div class="setting-name">勿扰时间</div>
            <div class="setting-desc">设置勿扰时间段，期间不会收到通知</div>
          </div>
          <div class="setting-control">
            <label class="switch">
              <input 
                type="checkbox" 
                v-model="settings.doNotDisturb"
                @change="updateSetting('doNotDisturb', settings.doNotDisturb)"
              >
              <span class="slider"></span>
            </label>
          </div>
        </div>

        <div v-if="settings.doNotDisturb" class="sub-settings">
          <div class="time-setting">
            <label>开始时间：</label>
            <input 
              type="time" 
              v-model="settings.doNotDisturbStart"
              @change="updateSetting('doNotDisturbStart', settings.doNotDisturbStart)"
              class="time-input"
            >
          </div>
          <div class="time-setting">
            <label>结束时间：</label>
            <input 
              type="time" 
              v-model="settings.doNotDisturbEnd"
              @change="updateSetting('doNotDisturbEnd', settings.doNotDisturbEnd)"
              class="time-input"
            >
          </div>
        </div>
      </div>
    </div>
  </page-layout>
</template>

<script setup>
import { ref, onMounted, inject } from 'vue'
import PageLayout from '../components/PageLayout.vue'

const toast = inject('toast')

// 通知设置
const settings = ref({
  newMessage: true,
  newMatch: true,
  newLike: true,
  messageSound: true,
  vibration: true,
  showOnlineStatus: true,
  readReceipt: true,
  doNotDisturb: false,
  doNotDisturbStart: '22:00',
  doNotDisturbEnd: '08:00'
})

// 更新设置
const updateSetting = (key, value) => {
  // 这里应该调用API保存设置到服务器
  console.log(`更新设置: ${key} = ${value}`)
  
  // 保存到本地存储
  localStorage.setItem('notificationSettings', JSON.stringify(settings.value))
  
  toast.success('设置已保存')
}

// 加载设置
const loadSettings = () => {
  const saved = localStorage.getItem('notificationSettings')
  if (saved) {
    try {
      settings.value = { ...settings.value, ...JSON.parse(saved) }
    } catch (e) {
      console.error('加载通知设置失败:', e)
    }
  }
}

// 保存所有设置
const saveSettings = () => {
  // 这里应该调用API保存所有设置到服务器
  console.log('保存所有设置:', settings.value)
  
  // 保存到本地存储
  localStorage.setItem('notificationSettings', JSON.stringify(settings.value))
  
  toast.success('所有设置已保存')
}

onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.notification-settings {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.settings-section {
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  padding: 20px;
  margin: 0;
  border-bottom: 1px solid #f0f0f0;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f8f9fa;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
}

.setting-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.setting-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.setting-control {
  margin-left: 20px;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 30px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ddd;
  transition: 0.3s;
  border-radius: 30px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 24px;
  width: 24px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
  background-color: #ff8a8a;
}

input:checked + .slider:before {
  transform: translateX(22px);
}

/* 子设置 */
.sub-settings {
  margin: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
}

.time-setting {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.time-setting:last-child {
  margin-bottom: 0;
}

.time-setting label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-right: 15px;
  min-width: 80px;
}

.time-input {
  padding: 10px 12px;
  border: 2px solid #f0f0f0;
  border-radius: 8px;
  font-size: 14px;
  color: #333;
  background: white;
  transition: all 0.3s;
  outline: none;
}

.time-input:focus {
  border-color: #ff8a8a;
  box-shadow: 0 0 0 3px rgba(255, 138, 138, 0.1);
}

/* 确定按钮 */
.confirm-btn {
  padding: 8px 20px;
  background: #ff8a8a;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 60px;
}

.confirm-btn:hover {
  background: #ff7575;
}

@media (max-width: 480px) {
  .notification-settings {
    padding: 15px;
  }
  
  .section-title,
  .setting-item {
    padding: 15px;
  }
  
  .sub-settings {
    margin: 15px;
    padding: 15px;
  }
  
  .setting-control {
    margin-left: 15px;
  }
}
</style> 