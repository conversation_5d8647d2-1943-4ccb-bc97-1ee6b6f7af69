import { defineStore } from 'pinia'
import { nextTick } from 'vue'
import socketService from '../services/socketService'

export const useMessageStore = defineStore('message', {
  state: () => ({
    contacts: [],
    matches: [],
    messages: {},
    currentUser: {
      id: null,
      name: '',
      avatar: '/default.png',
      token: null
    },
    socketConnected: false,
    currentChatContactId: null // 当前正在聊天的联系人ID
  }),

  getters: {
    getMessagesByContactId: state => contactId => state.messages[contactId] || [],
    totalUnreadCount: state => state.contacts.reduce((sum, contact) => sum + contact.unreadCount, 0),
    getContactById: state => contactId => state.contacts.find(contact => contact.id === contactId)
  },

  actions: {
    // 初始化
    init(userData) {
      this.currentUser = { ...this.currentUser, ...userData }
      this.initSocket()
    },

    // Socket 连接
    initSocket() {
      if (this.socketConnected || !this.currentUser.token) return

      socketService.configure({
        url: import.meta.env.VITE_WS_URL,
        maxReconnectAttempts: parseInt(import.meta.env.VITE_WS_MAX_RECONNECT_ATTEMPTS) || 5,
        reconnectInterval: parseInt(import.meta.env.VITE_WS_RECONNECT_INTERVAL) || 3000
      })

      socketService.connect(this.currentUser.token)

      socketService.on('connect', () => {
        this.socketConnected = true
      })

      socketService.on('disconnect', () => {
        this.socketConnected = false
      })

      socketService.on('message', this.handleMessage)
    },

    disconnectSocket() {
      socketService.disconnect()
      this.socketConnected = false
    },

    // Socket消息处理（只接收，不发送）
    handleMessage(message) {
      console.log('📡 收到Socket消息:', message)
      
      if (message.type === 'message') {
        // 接收新消息，传递服务器返回的消息ID
        this.receiveMessage(message.senderId, message.content, message.contentType || 'text', message.id)
        console.log('📧 收到新消息:', { 
          from: message.senderId, 
          content: message.contentType === 'image' ? '[图片]' : message.content,
          id: message.id
        })
      } else if (message.type === 'match') {
        // 匹配成功通知
        console.log('🎉 收到匹配通知:', message)
        this.loadMatches() // 重新加载匹配列表
      } else if (message.type === 'status') {
        // 消息状态更新 - 需要找到对应的contactId
        for (const contactId in this.messages) {
          const messageIndex = this.messages[contactId].findIndex(msg => msg.id === message.messageId)
          if (messageIndex !== -1) {
            this.updateMessageStatus(contactId, message.messageId, message.status)
            break
          }
        }
      } else if (message.type === 'read_receipt') {
        // 对方已读回执
        console.log('👀 对方已读消息:', message)
        // 可以在这里更新消息的已读状态
      }
    },

    // 发送消息（通过API，Socket只负责接收）
    async sendMessage(contactId, content, type = 'text') {
      const tempMessage = this.createMessage(content, type, true)
      this.addMessage(contactId, tempMessage)
      this.updateContact(contactId, content, type)

      try {
        const { messageService } = await import('../services/messageService')
        const response = await messageService.sendMessage({
          receiverId: contactId,
          content,
          contentType: type
        })

        this.updateMessageStatus(contactId, tempMessage.id, 'sent', response.id)
        console.log('📤 消息发送成功:', { contactId, content, type })
        return tempMessage
      } catch (error) {
        console.error('📤 发送消息失败:', error)
        this.updateMessageStatus(contactId, tempMessage.id, 'failed')
        throw error
      }
    },

    // 统一的消息状态更新方法
    updateMessageStatus(contactId, messageId, status, newId = null) {
      const messages = this.messages[contactId]
      if (!messages) return

      const messageIndex = messages.findIndex(msg => msg.id === messageId)
      if (messageIndex !== -1) {
        messages[messageIndex].status = status
        if (newId) messages[messageIndex].id = newId
        console.log(`📤 消息状态已更新为 ${status}:`, messages[messageIndex])

        // 确保响应式更新
        this.messages[contactId] = [...messages]
      }
    },

    // 接收消息
    receiveMessage(contactId, content, type = 'text', messageId) {
      const message = this.createMessage(content, type, false, messageId)
      console.log('📨 接收新消息:', { contactId, content, type, messageId })

      this.addMessage(contactId, message)

      const isCurrentlyViewing = this.currentChatContactId === contactId
      this.handleReceivedMessage(contactId, content, type, message, messageId, isCurrentlyViewing)

      console.log('📨 消息已添加到列表:', {
        contactId,
        messageCount: this.messages[contactId]?.length,
        isCurrentlyViewing,
        messageStatus: message.status,
        unreadCount: this.getContactById(contactId)?.unreadCount || 0
      })
      return message
    },

    // 处理接收到的消息的已读状态和未读计数
    handleReceivedMessage(contactId, content, type, message, messageId, isCurrentlyViewing) {
      if (isCurrentlyViewing) {
        console.log('📨 用户正在查看此聊天，消息立即标记为已读')
        this.updateContact(contactId, content, type, false)
        message.status = 'read'
        this.callMarkAsReadAPI(contactId, messageId ? [messageId] : [])
      } else {
        this.updateContact(contactId, content, type, true)
      }
    },

    // 创建消息对象
    createMessage(content, type, isSelf, messageId) {
      return {
        id: messageId || Date.now().toString(),
        content,
        type,
        time: new Date(),
        isSelf,
        status: isSelf ? 'sending' : 'received',
        ...(type === 'image' ? { loaded: false, loading: true } : {})
      }
    },

    // 添加消息
    addMessage(contactId, message) {
      if (!this.messages[contactId]) {
        this.messages[contactId] = []
      }
      this.messages[contactId].push(message)
      
      // 确保响应式更新
      this.messages[contactId] = [...this.messages[contactId]]
    },

    // 更新联系人信息
    updateContact(contactId, content, type, isReceived = false) {
      const contactIndex = this.contacts.findIndex(c => c.id === contactId)
      if (contactIndex === -1) {
        console.warn('联系人不存在:', contactId)
        return
      }

      const contact = this.contacts[contactIndex]
      
      // 更新联系人信息
      const updatedContact = {
        ...contact,
        lastMessage: {
          content: type === 'text' ? content : '[图片]',
          time: new Date()
        },
        lastMessageTime: new Date(),
        unreadCount: isReceived ? contact.unreadCount + 1 : contact.unreadCount
      }

      // 替换联系人对象以触发响应式更新
      this.contacts[contactIndex] = updatedContact

      // 置顶联系人（移到列表第一位）
      if (contactIndex > 0) {
        const contactToMove = this.contacts.splice(contactIndex, 1)[0]
        this.contacts.unshift(contactToMove)
      }

      // 确保整个联系人列表的响应式更新
      this.contacts = [...this.contacts]

      console.log('📞 联系人信息已更新:', { 
        contactId, 
        lastMessage: updatedContact.lastMessage.content,
        unreadCount: updatedContact.unreadCount,
        isReceived 
      })
    },

    // 发送图片消息（先上传图片，再发送消息）
    async sendImageMessage(contactId, imageFile) {
      try {
        // 先生成预览URL在本地显示
        const previewUrl = URL.createObjectURL(imageFile)
        const tempMessage = this.createMessage(previewUrl, 'image', true)
        tempMessage.loading = true
        tempMessage.loaded = false
        
        this.addMessage(contactId, tempMessage)
        this.updateContact(contactId, '[图片]', 'image')

        // 通过API上传图片
        const { messageService } = await import('../services/messageService')
        const uploadResponse = await messageService.uploadChatImage(imageFile)
        const imageUrl = uploadResponse.url

        // 发送图片消息
        const response = await messageService.sendMessage({
          receiverId: contactId,
          content: imageUrl,
          contentType: 'image'
        })

        // 更新消息状态和内容（确保响应式更新）
        const messages = this.messages[contactId]
        const messageIndex = messages.findIndex(msg => msg.id === tempMessage.id)
        if (messageIndex !== -1) {
          messages[messageIndex].status = 'sent'
          messages[messageIndex].content = imageUrl
          messages[messageIndex].id = response.id || messages[messageIndex].id
          messages[messageIndex].loading = false
          messages[messageIndex].loaded = true
          
          // 确保响应式更新
          this.messages[contactId] = [...messages]
        }

        // 释放预览URL
        URL.revokeObjectURL(previewUrl)

        console.log('📤 图片发送成功:', { contactId, imageUrl })
        return tempMessage
      } catch (error) {
        console.error('📤 发送图片失败:', error)
        
        // 发送失败，标记消息状态
        const messages = this.messages[contactId]
        const messageIndex = messages.findIndex(msg => msg.id === tempMessage.id)
        if (messageIndex !== -1) {
          messages[messageIndex].status = 'failed'
          messages[messageIndex].loading = false
          console.log('📤 图片消息状态已更新为 failed:', messages[messageIndex])
          
          // 确保响应式更新
          this.messages[contactId] = [...messages]
        }
        throw error
      }
    },

    // 标记已读（通过API，Socket只负责接收）
    async markAsRead(contactId) {
      try {
        const contactIndex = this.contacts.findIndex(c => c.id === contactId)
        if (contactIndex !== -1) {
          const contact = this.contacts[contactIndex]
          const originalUnreadCount = contact.unreadCount
          
          console.log('📖 标记已读:', { contactId, unreadCount: originalUnreadCount })
          
          // 立即本地更新 - 创建新的联系人对象以触发响应式更新
          const updatedContact = {
            ...contact,
            unreadCount: 0
          }
          this.contacts[contactIndex] = updatedContact

          const messages = this.messages[contactId]
          
          if (messages) {
            let hasUpdates = false
            messages.forEach(msg => {
              if (!msg.isSelf && msg.status !== 'read') {
                msg.status = 'read'
                hasUpdates = true
              }
            })
            
            // 如果有状态更新，确保响应式更新
            if (hasUpdates) {
              this.messages[contactId] = [...messages]
            }
          }

          // 确保联系人列表的响应式更新
          this.contacts = [...this.contacts]
          
          // 总是调用API标记已读，让服务器处理所有未读消息
          this.callMarkAsReadAPI(contactId, [])
        }
      } catch (error) {
        console.error('📖 标记已读失败:', error)
      }
    },

    // 直接调用API标记消息已读（不依赖未读计数）
    async callMarkAsReadAPI(contactId, messageIds = []) {
      try {
        console.log('📖 调用API标记已读:', { contactId, messageIds })
        
        const { messageService } = await import('../services/messageService')
        
        // 如果没有有效的messageIds，传递空数组让服务器标记该联系人的所有未读消息
        const validMessageIds = messageIds.filter(id => id && id !== 'undefined' && typeof id !== 'undefined')
        
        const response = await messageService.markAsRead({
          contactId,
          messageIds: validMessageIds.length > 0 ? validMessageIds : [] // 传递空数组让服务器处理所有未读消息
        })
        
        console.log('📖 已读状态已同步到服务器，响应:', response)
        
        // API调用成功后，强制刷新联系人列表以确保未读计数同步
        await this.loadContacts()
        
      } catch (apiError) {
        console.error('📖 API标记已读失败:', apiError)
        // API失败不回滚本地状态，避免用户体验问题
      }
    },

    // 强制刷新联系人数据（解决返回时数据不同步问题）
    async forceRefreshContacts() {
      console.log('🔄 强制刷新联系人数据')
      try {
        await this.loadContacts()
        console.log('🔄 联系人数据刷新完成')
      } catch (error) {
        console.error('🔄 刷新联系人数据失败:', error)
      }
    },



    // 图片加载完成
    setImageLoaded(contactId, messageIndex) {
      const messages = this.messages[contactId]
      if (messages && messages[messageIndex]) {
        messages[messageIndex].loaded = true
        messages[messageIndex].loading = false
        
        // 确保响应式更新
        this.messages[contactId] = [...messages]
      }
    },

    // 清空聊天记录
    clearChat(contactId) {
      this.messages[contactId] = []
    },

    // 设置联系人列表
    setContacts(contacts) {
      this.contacts = contacts
    },

    // 设置匹配列表
    setMatches(matches) {
      this.matches = matches
    },

    // 加载联系人列表
    async loadContacts() {
      try {
        const { messageService } = await import('../services/messageService')
        const response = await messageService.getContacts()
        this.contacts = response || []
        console.log('📞 联系人列表已加载:', this.contacts)
      } catch (error) {
        console.error('获取联系人列表失败:', error)
        this.contacts = []
      }
    },

    // 加载匹配列表
    async loadMatches() {
      try {
        const { matchService } = await import('../services/matchService')
        const response = await matchService.getMatches()
        this.matches = response || []
      } catch (error) {
        console.error('获取匹配列表失败:', error)
        this.matches = []
      }
    },

    // 加载聊天记录
    async loadChatHistory(contactId, params = {}) {
      try {
        const { messageService } = await import('../services/messageService')
        const response = await messageService.getChatHistory(contactId, params)
        
        console.log('📥 API返回的聊天记录:', response)
        console.log('📥 当前用户ID:', this.currentUser.id)
        
        if (response && response.data) {
          // 转换时间格式和消息格式
          const messages = response.data.map(msg => ({
            id: msg.id,
            content: msg.content,
            type: msg.contentType || 'text',
            time: new Date(msg.time),
            isSelf: msg.senderId === this.currentUser.id,
            status: msg.senderId === this.currentUser.id ? 'sent' : 'received',
            loaded: msg.contentType === 'image' ? true : undefined,
            loading: msg.contentType === 'image' ? false : undefined
          }))
          
          // 按时间正序排列（最旧的在前）
          messages.sort((a, b) => new Date(a.time) - new Date(b.time))
          
          this.messages[contactId] = messages
          
          console.log('📥 加载聊天记录成功:', { 
            contactId, 
            messageCount: messages.length,
            hasMore: response.pagination?.hasMore || false
          })
          
          return {
            messages,
            hasMore: response.pagination?.hasMore || false
          }
        }
        return { messages: [], hasMore: false }
      } catch (error) {
        console.error('获取聊天记录失败:', error)
        this.messages[contactId] = []
        return { messages: [], hasMore: false }
      }
    },

    // 初始化数据
    async initData() {
      await Promise.all([
        this.loadContacts(),
        this.loadMatches()
      ])
    },

    // 设置当前聊天的联系人ID（进入聊天页面时调用）
    setCurrentChatContact(contactId) {
      console.log('📱 设置当前聊天联系人:', contactId)
      this.currentChatContactId = contactId
    },

    // 清除当前聊天的联系人ID（离开聊天页面时调用）
    clearCurrentChatContact() {
      console.log('📱 清除当前聊天联系人:', this.currentChatContactId)
      this.currentChatContactId = null
    }
  }
})
