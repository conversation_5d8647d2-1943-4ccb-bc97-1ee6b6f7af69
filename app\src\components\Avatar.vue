<template>
  <div class="avatar-wrapper" @click="$emit('click')">
    <div
      class="avatar"
      :class="[`avatar-${size}`, { 'avatar-with-border': border }, { 'avatar-fill': fill }]"
    >
      <img :src="avatarSrc" :alt="alt" @error="handleError" @load="handleLoad" />
    </div>
    <span
      v-if="badge"
      class="avatar-badge"
      :class="[{ 'badge-dot': badgeType === 'dot' }, `badge-${size}`]"
    >
      {{ badgeType === 'dot' ? '' : badge }}
    </span>
  </div>
</template>

<script setup>
  import { ref, computed } from 'vue'
  import { getImageUrl, getDefaultAvatarUrl } from '../config'

  // 定义事件
  const _emit = defineEmits(['click'])

  // 默认头像
  const defaultAvatar = getDefaultAvatarUrl()

  // 图片加载状态
  const imageError = ref(false)

  const _props = defineProps({
    src: {
      type: String,
      default: ''
    },
    alt: {
      type: String,
      default: 'avatar'
    },
    size: {
      type: String,
      default: 'medium', // small, medium, large, xlarge
      validator: value => ['small', 'medium', 'large', 'xlarge'].includes(value)
    },
    border: {
      type: Boolean,
      default: false
    },
    badge: {
      type: [String, Number],
      default: ''
    },
    badgeType: {
      type: String,
      default: 'number', // number, dot
      validator: value => ['number', 'dot'].includes(value)
    },
    fill: {
      type: Boolean,
      default: false
    }
  })

  // 计算最终的头像URL
  const avatarSrc = computed(() => {
    // 如果图片加载失败，使用默认头像
    if (imageError.value) {
      return defaultAvatar
    }

    // 如果没有传入src或src为空，使用默认头像
    if (!_props.src || _props.src === '' || _props.src === null || _props.src === undefined) {
      return defaultAvatar
    }

    // 使用getImageUrl处理传入的src
    return getImageUrl(_props.src)
  })

  // 处理图片加载错误
  const handleError = (e) => {
    console.warn('头像加载失败:', _props.src)
    imageError.value = true
    // 确保设置默认头像
    e.target.src = defaultAvatar
  }

  // 处理图片加载成功
  const handleLoad = () => {
    imageError.value = false
  }
</script>

<style scoped>
  .avatar-wrapper {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
    transition: transform var(--transition-fast) ease;
    flex-shrink: 0;
  }

  .avatar-wrapper:hover {
    transform: scale(1.05);
  }

  .avatar {
    position: relative;
    border-radius: 50%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
  }

  .avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
  }

  .avatar-small {
    width: 40px;
    height: 40px;
  }

  .avatar-medium {
    width: 60px;
    height: 60px;
  }

  .avatar-large {
    width: 80px;
    height: 80px;
  }

  .avatar-xlarge {
    width: 100px;
    height: 100px;
  }

  .avatar-with-border {
    border: 2px solid var(--color-primary);
    padding: 2px;
  }

  .avatar-badge {
    position: absolute;
    top: 0;
    right: 0;
    min-width: 16px;
    height: 16px;
    background-color: var(--color-primary);
    border-radius: 8px;
    color: white;
    font-size: var(--font-size-xs);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--spacing-xs);
    border: 1.5px solid #ffffff;
    z-index: 10;
    box-sizing: content-box;
    transform: translate(30%, -30%);
  }

  .badge-small {
    min-width: 14px;
    height: 14px;
    font-size: calc(var(--font-size-xs) - 1px);
  }

  .badge-medium {
    min-width: 16px;
    height: 16px;
    font-size: var(--font-size-xs);
  }

  .badge-large {
    min-width: 18px;
    height: 18px;
    font-size: calc(var(--font-size-xs) + 1px);
  }

  .badge-xlarge {
    min-width: 20px;
    height: 20px;
    font-size: calc(var(--font-size-xs) + 2px);
  }

  .badge-dot {
    min-width: var(--spacing-sm);
    height: var(--spacing-sm);
    padding: 0;
    border-radius: calc(var(--spacing-sm) / 2);
  }

  .badge-small.badge-dot {
    min-width: calc(var(--spacing-xs) + 2px);
    height: calc(var(--spacing-xs) + 2px);
    border-radius: calc((var(--spacing-xs) + 2px) / 2);
  }

  .badge-large.badge-dot {
    min-width: calc(var(--spacing-md) - 6px);
    height: calc(var(--spacing-md) - 6px);
    border-radius: calc((var(--spacing-md) - 6px) / 2);
  }

  .badge-xlarge.badge-dot {
    min-width: var(--spacing-md);
    height: var(--spacing-md);
    border-radius: calc(var(--spacing-md) / 2);
  }

  .avatar-fill {
    width: 100% !important;
    height: 100% !important;
    max-width: none !important;
    max-height: none !important;
  }
</style>
