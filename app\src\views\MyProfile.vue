<template>
  <page-layout title="编辑资料" :show-back="true" hide-tab-bar>
    <template #header-right>
      <button class="confirm-btn" @click="saveProfile">保存</button>
    </template>

    <div class="header-section">
      <h1 class="username">{{ profile.username }}</h1>
    </div>

    <div class="form-container">
      <div class="form-item">
        <label>昵称</label>
        <input 
          v-model="profile.username" 
          type="text" 
          placeholder="请输入昵称"
          class="form-input"
        />
      </div>

      <div class="form-item">
        <label>性别</label>
        <div class="gender-group">
          <button 
            type="button"
            :class="['gender-btn', { active: profile.gender === 'male' }]"
            @click="profile.gender = 'male'"
          >
            男
          </button>
          <button 
            type="button"
            :class="['gender-btn', { active: profile.gender === 'female' }]"
            @click="profile.gender = 'female'"
          >
            女
          </button>
        </div>
      </div>

      <div class="form-item">
        <label>年龄</label>
        <input 
          v-model.number="profile.age" 
          type="number" 
          min="18" 
          max="100"
          placeholder="请输入年龄"
          class="form-input"
        />
      </div>

      <div class="form-item">
        <label>地区</label>
        <div class="region-container">
          <RegionSelector 
            v-model:province-code="profile.provinceCode"
            v-model:city-code="profile.cityCode"
            @change="handleRegionChange"
          />
        </div>
      </div>

      <div class="form-item">
        <label>个人介绍</label>
        <textarea 
          v-model="profile.bio" 
          placeholder="简单介绍一下自己..."
          class="form-textarea"
          rows="3"
        ></textarea>
      </div>
    </div>
  </page-layout>
</template>

<script setup>
import { ref, inject, onMounted } from 'vue'
import PageLayout from '../components/PageLayout.vue'
import RegionSelector from '../components/RegionSelector.vue'
import { userService } from '../services/userService'
import { useUserStore } from '../stores/userStore'

const toast = inject('toast')
const userStore = useUserStore()

const profile = ref({
  username: '',
  nickname: '',
  gender: '',
  age: 18,
  provinceCode: '',
  cityCode: '',
  bio: ''
})

// 获取用户资料
const fetchUserProfile = async () => {
  try {
    const userProfile = await userService.getProfile()
    if (userProfile) {
      profile.value = {
        username: userProfile.username || '',
        nickname: userProfile.nickname || '',
        gender: userProfile.gender || '',
        age: userProfile.age || 18,
        provinceCode: userProfile.provinceCode || '',
        cityCode: userProfile.cityCode || '',
        bio: userProfile.bio || ''
      }
    }
  } catch (error) {
    console.error('获取用户资料失败:', error)
    toast.error('获取用户资料失败')
  }
}

const validateForm = () => {
  if (!profile.value.nickname?.trim()) {
    toast.error('请输入昵称')
    return false
  }
  if (!profile.value.gender) {
    toast.error('请选择性别')
    return false
  }
  if (!profile.value.age || profile.value.age < 18 || profile.value.age > 100) {
    toast.error('年龄必须在18-100岁之间')
    return false
  }
  if (!profile.value.provinceCode) {
    toast.error('请选择地区')
    return false
  }
  return true
}

// 处理地区变化
const handleRegionChange = (regionData) => {
  profile.value.provinceCode = regionData.provinceCode
  profile.value.cityCode = regionData.cityCode
}

const saveProfile = async () => {
  if (validateForm()) {
    try {
      const profileData = {
        nickname: profile.value.nickname,
        gender: profile.value.gender,
        age: profile.value.age,
        provinceCode: profile.value.provinceCode,
        cityCode: profile.value.cityCode,
        bio: profile.value.bio
      }
      
      await userService.updateProfile(profileData)
      
      // 更新本地用户信息
      const updatedInfo = {
        ...userStore.userInfo,
        ...profileData
      }
      userStore.setUserInfo(updatedInfo)
      
      toast.success('保存成功')
    } catch (error) {
      console.error('保存资料失败:', error)
      toast.error('保存失败，请重试')
    }
  }
}

onMounted(() => {
  fetchUserProfile()
})
</script>

<style scoped>
/* 头部区域 */
.header-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #ff8a8a 0%, #ffb3b3 100%);
  color: white;
}

.username {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

/* 表单区域 */
.form-container {
  padding: 30px 20px;
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.form-item {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-item label {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

/* 下划线输入框样式 */
.form-input,
.form-textarea {
  padding: 10px 0;
  border: none;
  border-bottom: 2px solid #f0f0f0;
  background: transparent;
  font-size: 16px;
  color: #333;
  transition: all 0.3s;
  outline: none;
}

.form-input:focus,
.form-textarea:focus {
  border-bottom-color: #ff8a8a;
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: #ccc;
}

.form-textarea {
  resize: none;
  font-family: inherit;
  line-height: 1.5;
  min-height: 60px;
}

/* 性别选择 */
.gender-group {
  display: flex;
  gap: 15px;
}

.gender-btn {
  flex: 1;
  padding: 15px;
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.3s;
}

.gender-btn:hover {
  border-color: #ff8a8a;
  color: #ff8a8a;
}

.gender-btn.active {
  background: #ff8a8a;
  border-color: #ff8a8a;
  color: white;
}

/* 确定按钮 */
.confirm-btn {
  padding: 8px 20px;
  background: #ff8a8a;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 60px;
}

.confirm-btn:hover {
  background: #ff7575;
}

/* 地区选择器 */
.region-container {
  margin-top: 10px;
  width: 100%;
  display: block;
  box-sizing: border-box;
}

.region-container :deep(.region-selector) {
  width: 100%;
}

.region-container :deep(.selector-row) {
  width: 100%;
}

@media (max-width: 480px) {
  .header-section {
    padding: 30px 15px;
  }
  
  .form-container {
    padding: 20px 15px;
  }
  
  .gender-group {
    gap: 10px;
  }
}
</style> 