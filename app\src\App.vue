<script>
import { watch, onMounted } from 'vue'
import { useMessageStore } from './stores/messageStore'
import { useUserStore } from './stores/userStore'
import config from './config'

export default {
  name: 'App',
  setup() {
    const messageStore = useMessageStore()
    const userStore = useUserStore()
    
    // 设置应用标题
    onMounted(() => {
      document.title = config.APP_TITLE
      console.log(`📱 ${config.APP_TITLE} 启动成功`)
      console.log(`🔗 API地址: ${config.API_BASE_URL}`)
      console.log(`📡 WebSocket: ${config.WS_URL}`)
    })
    
    // 监听用户登录状态变化
    watch(() => userStore.isLogin, (isLogin) => {
      if (isLogin) {
        // 用户登录时初始化 MessageStore 和 Socket 连接
        const userData = {
          id: userStore.userInfo.id,
          name: userStore.userInfo.nickname || userStore.userInfo.username,
          avatar: userStore.userInfo.avatar,
          token: userStore.token
        }
        messageStore.init(userData)
        messageStore.initData()
      } else {
        // 用户登出时断开 Socket 连接
        messageStore.disconnectSocket()
      }
    }, { immediate: true }) // immediate: true 表示立即执行一次
    
    return {}
  }
}
</script>

<template>
  <router-view v-slot="{ Component }">
    <transition name="fade" mode="out-in">
      <component :is="Component" :key="$route.path" />
    </transition>
  </router-view>
</template>

<style>
  #app {
    width: 100%;
    height: 100%;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
</style>
