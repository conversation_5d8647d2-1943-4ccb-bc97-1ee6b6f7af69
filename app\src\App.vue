<script>
import { watch, onMounted, ref } from 'vue'
import { useMessageStore } from './stores/messageStore'
import { useUserStore } from './stores/userStore'
import { userService } from './services/userService'
import config from './config'

export default {
  name: 'App',
  setup() {
    const messageStore = useMessageStore()
    const userStore = useUserStore()
    const regionDataLoaded = ref(false)

    // 预加载省市数据（需要登录后才能访问）
    const preloadRegionData = async () => {
      if (regionDataLoaded.value) return // 避免重复加载

      try {
        console.log('正在预加载省市数据...')
        await userService.getRegions()
        regionDataLoaded.value = true
        console.log('省市数据预加载完成')
      } catch (error) {
        console.error('省市数据预加载失败:', error)
      }
    }

    // 设置应用标题
    onMounted(() => {
      document.title = config.APP_TITLE
      console.log(`📱 ${config.APP_TITLE} 启动成功`)
      console.log(`🔗 API地址: ${config.API_BASE_URL}`)
      console.log(`📡 WebSocket: ${config.WS_URL}`)
    })

    // 监听用户登录状态变化
    watch(() => userStore.isLogin, async (isLogin) => {
      if (isLogin && userStore.userInfo && userStore.token) {
        // 用户登录时初始化 MessageStore 和 Socket 连接
        const userData = {
          id: userStore.userInfo.id,
          name: userStore.userInfo.nickname || userStore.userInfo.username || '用户',
          avatar: userStore.userInfo.avatar || '',
          token: userStore.token
        }
        messageStore.init(userData)
        messageStore.initData()

        // 登录后预加载地区数据
        await preloadRegionData()
      } else {
        // 用户登出时断开 Socket 连接
        messageStore.disconnectSocket()
        // 重置地区数据加载状态
        regionDataLoaded.value = false
      }
    }, { immediate: true }) // immediate: true 表示立即执行一次

    return {}
  }
}
</script>

<template>
  <router-view v-slot="{ Component }">
    <transition name="fade" mode="out-in">
      <component :is="Component" :key="$route.path" />
    </transition>
  </router-view>
</template>

<style>
  #app {
    width: 100%;
    height: 100%;
  }

  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }
</style>
