<template>
  <div class="tab-bar" :class="{ 'has-safe-area': hasSafeArea }">
    <div v-for="(item, index) in visibleItems" :key="index" class="tab-item"
      :class="{ active: currentPath === item.path, placeholder: item.isPlaceholder }" @click="switchTab(item.path)">
      <div class="tab-icon-wrapper">
        <div class="tab-icon" :class="currentPath === item.path ? item.activeIcon : item.icon">
          <!-- 首页图标 -->
          <svg v-if="item.icon === 'icon-home'" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round">
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" />
            <polyline points="9 22 9 12 15 12 15 22" />
          </svg>

          <!-- 消息图标 -->
          <svg v-if="item.icon === 'icon-message'" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round">
            <path
              d="M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z" />
          </svg>

          <!-- 喜欢图标 -->
          <svg v-if="item.icon === 'icon-heart'" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round">
            <path
              d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" />
          </svg>

          <!-- 我的图标 -->
          <svg v-if="item.icon === 'icon-user'" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
            stroke-linejoin="round">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
            <circle cx="12" cy="7" r="4" />
          </svg>
        </div>
        <div v-if="getBadge(item)" class="badge" :class="{ 'badge-dot': getBadge(item) === 'dot' }">
          {{ getBadge(item) !== 'dot' ? getBadge(item) : '' }}
        </div>
      </div>
      <span class="tab-text">{{ item.text }}</span>
    </div>

    <!-- 中心按钮 -->
    <div class="center-button" @click="handleCenterButtonClick">
      <div class="center-button-inner">
        <!-- 广场图标 -->
        <svg v-if="!isInSquare" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
          fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="3" y="3" width="7" height="7" />
          <rect x="14" y="3" width="7" height="7" />
          <rect x="14" y="14" width="7" height="7" />
          <rect x="3" y="14" width="7" height="7" />
        </svg>

        <!-- 发帖图标 -->
        <svg v-else xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="12" y1="5" x2="12" y2="19" />
          <line x1="5" y1="12" x2="19" y2="12" />
        </svg>
      </div>
      <div class="center-button-pulse" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessageStore } from '../stores/messageStore'
import { useTabBarStore } from '../stores/tabBarStore'

// 获取路由实例
const route = useRoute()
const router = useRouter()
const messageStore = useMessageStore()
const tabBarStore = useTabBarStore()

// 定义属性
const props = defineProps({
  // 是否有安全区
  hasSafeArea: {
    type: Boolean,
    default: true
  }
})

// 定义事件
const emit = defineEmits(['centerClick'])

// 当前路径
const currentPath = ref('/')

// 是否在广场页面
const isInSquare = computed(() => {
  const isSquare = currentPath.value === '/square'
  tabBarStore.setInSquare(isSquare)
  return isSquare
})

// 计算最终标签项
const tabItems = computed(() => {
  return tabBarStore.items
})

// 计算可见的标签项（为中心按钮留出空间）
const visibleItems = computed(() => {
  const items = [...tabItems.value]
  const midIndex = Math.floor(items.length / 2)

  // 如果是偶数个项目，在中间插入一个空项目
  if (items.length % 2 === 0) {
    items.splice(midIndex, 0, { isPlaceholder: true })
  } else {
    // 如果是奇数个项目，将中间的项目替换为空项目
    items[midIndex] = { isPlaceholder: true }
  }

  return items
})

// 获取标签徽章 - 使用computed确保响应式更新
const getBadge = computed(() => {
  return (item) => {
    // 如果是消息标签，显示未读消息总数
    if (item.path === '/message') {
      const count = messageStore.totalUnreadCount
      if (count > 0) {
        return count > 99 ? '99+' : count.toString()
      }
    } else if (item.badge) {
      return item.badge
    }
    return null
  }
})

// 切换标签
const switchTab = path => {
  // 如果路径相同或无效，不进行导航
  if (!path || path === currentPath.value) {
    return
  }

  // 使用replace代替push，避免历史堆栈问题
  router.replace(path).catch(_err => {
    // 路由导航错误处理
  })
}

// 处理中心按钮点击
const handleCenterButtonClick = () => {
  const targetPath = tabBarStore.handleCenterClick()
  router.push(targetPath)
  emit('centerClick')
}

// 监听路由变化
watch(
  () => route.path,
  newPath => {
    currentPath.value = newPath
  },
  { immediate: true }
)

// 组件挂载时初始化当前路径
onMounted(() => {
  currentPath.value = route.path
})
</script>

<style scoped>
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--height-tabbar);
  background-color: #fff;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
  z-index: 100;
  border-top: 1px solid rgba(0, 0, 0, 0.03);
}

.tab-bar.has-safe-area {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--font-color-light);
  font-size: var(--font-size-sm);
  transition: all 0.3s;
  position: relative;
  padding: 12px 0;
}

.tab-item.placeholder {
  pointer-events: none;
  opacity: 0;
}

.tab-item.active {
  color: var(--color-primary);
}

.tab-icon-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 24px;
}

.tab-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
  line-height: 1;
}

.tab-icon svg {
  width: var(--font-size-xl);
  height: var(--font-size-xl);
  transform: none;
}

.tab-item.active .tab-icon {
  transform: none;
}

.tab-item.active .tab-icon svg {
  stroke: var(--color-primary);
  fill: none;
}

.tab-item.active .icon-heart svg {
  fill: var(--color-primary);
}

.tab-text {
  margin-top: 4px;
  transition: all 0.3s;
  font-size: var(--font-size-sm);
  font-weight: 500;
}

.badge {
  position: absolute;
  top: -5px;
  right: -8px;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
  padding: 0 4px;
  font-size: var(--font-size-xs);
  color: #fff;
  background-color: var(--color-primary);
  border-radius: 8px;
  text-align: center;
  font-weight: normal;
  box-shadow: 0 2px 4px rgba(255, 88, 100, 0.2);
}

.badge-dot {
  min-width: 8px;
  height: 8px;
  padding: 0;
  border-radius: 4px;
}

/* 中心按钮样式 */
.center-button {
  position: absolute;
  left: 50%;
  top: -20px;
  transform: translateX(-50%);
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--color-primary), var(--color-primary-gradient));
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 10px rgba(255, 88, 100, 0.4);
  z-index: 101;
  cursor: pointer;
  transition: all 0.3s;
}

.center-button:active {
  transform: translateX(-50%) scale(0.95);
}

.center-button-inner {
  width: 52px;
  height: 52px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--color-primary), var(--color-primary-gradient));
  display: flex;
  align-items: center;
  justify-content: center;
}

.center-button-inner svg {
  stroke: white;
  width: var(--font-size-xxl);
  height: var(--font-size-xxl);
}

.center-button-pulse {
  position: absolute;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: rgba(255, 88, 100, 0.3);
  animation: pulse 1.5s infinite;
  z-index: -1;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }

  70% {
    transform: scale(1.3);
    opacity: 0;
  }

  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}
</style>
