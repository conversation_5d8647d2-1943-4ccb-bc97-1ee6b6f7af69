// 这里我们使用浏览器原生的fetch API
// 实际项目中可以替换为axios等库

import { API_BASE_URL, REQUEST_TIMEOUT } from '../config'

// 基础配置
const BASE_URL = API_BASE_URL
const TIME_OUT = REQUEST_TIMEOUT // 超时时间，单位毫秒

/**
 * 获取认证token
 * @returns {string|null}
 */
const getAuthToken = () => {
  return localStorage.getItem('token')
}

/**
 * 统一的服务错误处理
 * @param {Error} error - 原始错误
 * @param {string} defaultMessage - 默认错误信息
 * @returns {Error} - 处理后的错误
 */
export const handleServiceError = (error, defaultMessage) => {
  return new Error(error.message || defaultMessage)
}

/**
 * 服务方法包装器，统一处理try-catch和错误处理
 * @param {Function} serviceMethod - 服务方法
 * @param {string} errorMessage - 错误信息
 * @returns {Function} - 包装后的方法
 */
export const withErrorHandler = (serviceMethod, errorMessage) => {
  return async (...args) => {
    try {
      return await serviceMethod(...args)
    } catch (error) {
      throw handleServiceError(error, errorMessage)
    }
  }
}

/**
 * 创建请求配置
 */
const createRequestConfig = (url, options = {}) => {
  const fullUrl = url.startsWith('http') ? url : `${BASE_URL}${url}`

  const defaultOptions = {
    headers: { 'Content-Type': 'application/json' },
    timeout: TIME_OUT,
    credentials: 'include'
  }

  const config = {
    ...defaultOptions,
    ...options,
    headers: { ...defaultOptions.headers, ...options.headers }
  }

  const token = getAuthToken()
  if (token) {
    config.headers['Authorization'] = `Bearer ${token}`
  }

  return { fullUrl, config }
}

/**
 * 处理响应数据
 */
const handleResponse = async (response) => {
  if (!response.ok) {
    throw new Error(`HTTP错误，状态: ${response.status}`)
  }

  const data = await response.json()

  if (data.code !== 0 && data.code !== 200) {
    throw new Error(data.message || '请求失败')
  }

  return data.pagination ? data : (data.data || data)
}

/**
 * 执行请求
 */
const executeRequest = async (fullUrl, config) => {
  const controller = new AbortController()
  config.signal = controller.signal
  const timeout = setTimeout(() => controller.abort(), config.timeout)

  try {
    const response = await fetch(fullUrl, config)
    clearTimeout(timeout)
    return await handleResponse(response)
  } catch (error) {
    clearTimeout(timeout)
    if (error.name === 'AbortError') {
      throw new Error('请求超时')
    }
    throw error
  }
}

/**
 * 基础请求函数
 */
export const request = async (url, options = {}) => {
  const { fullUrl, config } = createRequestConfig(url, options)
  return executeRequest(fullUrl, config)
}

/**
 * 文件上传请求函数
 */
export const uploadRequest = async (url, formData, options = {}) => {
  const uploadOptions = {
    method: 'POST',
    body: formData,
    headers: {},
    ...options
  }

  // 不设置Content-Type，让浏览器自动设置multipart/form-data
  delete uploadOptions.headers['Content-Type']

  const { fullUrl, config } = createRequestConfig(url, uploadOptions)
  return executeRequest(fullUrl, config)
}

/**
 * 构建带查询参数的URL
 */
const buildUrlWithParams = (url, params = {}) => {
  const queryString = new URLSearchParams(params).toString()
  return queryString ? `${url}?${queryString}` : url
}

// HTTP方法封装
export const http = {
  get: (url, params = {}) => request(buildUrlWithParams(url, params), { method: 'GET' }),

  post: (url, data) => request(url, {
    method: 'POST',
    body: JSON.stringify(data)
  }),

  put: (url, data) => request(url, {
    method: 'PUT',
    body: JSON.stringify(data)
  }),

  delete: (url, params = {}) => request(buildUrlWithParams(url, params), { method: 'DELETE' }),

  upload: (url, formData, options = {}) => uploadRequest(url, formData, options)
}

// 导出BASE_URL和getAuthToken供其他地方使用
export { BASE_URL, getAuthToken }
