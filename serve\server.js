const express = require('express')
const cors = require('cors')
const helmet = require('helmet')
const morgan = require('morgan')
const compression = require('compression')
const rateLimit = require('express-rate-limit')
const path = require('path')
const fs = require('fs-extra')
const http = require('http')
const WebSocket = require('ws')

// 导入路由
const authRoutes = require('./routes/auth')
const userRoutes = require('./routes/user')
const matchRoutes = require('./routes/match')
const messageRoutes = require('./routes/message')
const postRoutes = require('./routes/post')
const uploadRoutes = require('./routes/upload')

// 导入中间件
const { authenticateToken } = require('./middleware/auth')

// 初始化数据存储
const { initializeData } = require('./utils/dataStore')

const app = express()
const PORT = process.env.PORT || 3000

// 创建HTTP服务器
const server = http.createServer(app)

// 创建WebSocket服务器
const wss = new WebSocket.Server({ 
  server,
  path: '/ws'
})

// 存储WebSocket连接
const wsConnections = new Map()

// 中间件配置
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}))
// CORS 配置 - 支持环境变量配置
const corsOrigins = process.env.CORS_ORIGIN
  ? process.env.CORS_ORIGIN.split(',')
  : [
      `http://localhost:${process.env.DEV_PORT || '5173'}`,
      `http://localhost:${process.env.PORT || '3000'}`
    ]

app.use(cors({
  origin: corsOrigins,
  credentials: true
}))
app.use(compression())
app.use(morgan('combined'))

// 请求体大小限制 - 支持环境变量配置
const requestLimit = process.env.REQUEST_LIMIT || '10mb'

// 为非文件上传路由配置JSON和URL编码解析
app.use((req, res, next) => {
  // 跳过文件上传相关的路由
  if (req.path.includes('/upload') ||
      req.path.includes('/avatar') ||
      req.path.includes('/photos')) {
    return next()
  }

  // 对其他路由应用JSON和URL编码解析
  express.json({ limit: requestLimit })(req, res, () => {
    express.urlencoded({ extended: true, limit: requestLimit })(req, res, next)
  })
})

// 静态文件服务
app.use('/uploads', express.static(path.join(__dirname, 'uploads')))

// 限流配置 - 支持环境变量配置
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 3 * 60 * 1000, // 默认3分钟
  max: parseInt(process.env.RATE_LIMIT_MAX) || 1000, // 默认每个IP最多1000个请求
  message: {
    code: 429,
    message: process.env.RATE_LIMIT_MESSAGE || '请求过于频繁，请稍后再试'
  }
})
app.use('/api', limiter)

// 路由配置
app.use('/api/auth', authRoutes)
app.use('/api/user', authenticateToken, userRoutes)
app.use('/api/matches', authenticateToken, matchRoutes)
app.use('/api/likes', authenticateToken, matchRoutes)
app.use('/api/messages', authenticateToken, messageRoutes)
app.use('/api/posts', authenticateToken, postRoutes)
app.use('/api/comments', authenticateToken, postRoutes)
app.use('/api/upload', authenticateToken, uploadRoutes)

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    code: 0,
    message: '服务运行正常',
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    }
  })
})

// 根路径
app.get('/', (req, res) => {
  res.json({
    code: 0,
    message: '社交匹配应用API服务',
    data: {
      version: '1.0.0',
      docs: '/api-docs',
      health: '/health'
    }
  })
})

// WebSocket连接处理
wss.on('connection', (ws, req) => {
  // 从查询参数获取token
  const url = new URL(req.url, `http://${req.headers.host}`)
  const token = url.searchParams.get('token')
  
  if (!token) {
    ws.close(1008, '缺少认证token')
    return
  }
  
  try {
    const jwt = require('jsonwebtoken')
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key')
    const userId = decoded.userId
    
    // 存储连接
    wsConnections.set(userId, ws)
    
    // 发送连接成功消息
    ws.send(JSON.stringify({
      type: 'connected',
      message: '连接成功'
    }))
    
    // 处理消息
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data)
        handleWebSocketMessage(userId, message)
      } catch (error) {
        console.error('处理WebSocket消息失败:', error)
      }
    })
    
    // 处理断开连接
    ws.on('close', () => {
      wsConnections.delete(userId)
    })
    
  } catch (error) {
    ws.close(1008, 'token无效')
  }
})

// 处理WebSocket消息
function handleWebSocketMessage(senderId, message) {
  const { type, receiverId, content, contentType } = message
  
  if (type === 'message') {
    // 转发消息给接收者
    const receiverWs = wsConnections.get(receiverId)
    if (receiverWs && receiverWs.readyState === WebSocket.OPEN) {
      receiverWs.send(JSON.stringify({
        type: 'message',
        senderId,
        content,
        contentType: contentType || 'text',
        time: new Date().toISOString()
      }))
    }
    
    // 这里可以保存消息到数据库
    // saveMessage(senderId, receiverId, content, contentType)
  }
}

// 广播消息函数
function broadcastToUser(userId, message) {
  const ws = wsConnections.get(userId)
  if (ws && ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify(message))
  }
}

// 将广播函数添加到app对象，供路由使用
app.broadcastToUser = broadcastToUser

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error('服务器错误:', err)
  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  })
})

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在'
  })
})

// 启动服务器前初始化数据
async function startServer() {
  try {
    // 初始化数据存储
    await initializeData()
    
    // 启动服务器
    server.listen(PORT, () => {
      console.log(`🚀 服务器运行在端口 ${PORT}`)
    })
  } catch (error) {
    console.error('启动服务器失败:', error)
    process.exit(1)
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  console.log('收到SIGTERM信号，正在关闭服务器...')
  server.close(() => {
    console.log('服务器已关闭')
    process.exit(0)
  })
})

process.on('SIGINT', () => {
  console.log('收到SIGINT信号，正在关闭服务器...')
  server.close(() => {
    console.log('服务器已关闭')
    process.exit(0)
  })
})

// 启动服务器
startServer() 