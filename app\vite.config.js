import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// 从环境变量获取配置，提供默认值
const API_HOST = process.env.VITE_API_HOST || 'localhost'
const API_PORT = process.env.VITE_API_PORT || '3000'
const DEV_PORT = process.env.VITE_DEV_PORT || '5173'
const API_TARGET = `http://${API_HOST}:${API_PORT}`

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],

  // 定义全局常量
  define: {
    __API_BASE_URL__: JSON.stringify(process.env.VITE_API_BASE_URL || `${API_TARGET}/api`),
    __WS_URL__: JSON.stringify(process.env.VITE_WS_URL || `ws://${API_HOST}:${API_PORT}/ws`)
  },

  // 开发服务器配置
  server: {
    port: parseInt(DEV_PORT),
    host: true,
    // 代理配置（可选，如果需要避免CORS问题）
    proxy: {
      '/api': {
        target: API_TARGET,
        changeOrigin: true,
        secure: false
      },
      '/uploads': {
        target: API_TARGET,
        changeOrigin: true,
        secure: false
      }
    }
  }
})
