<template>
  <div class="filter-bar">
    <div class="filter-button" @click="toggleFilterDrawer">
      <i class="filter-icon">⚙</i>
      <span>筛选</span>
    </div>

    <div class="filter-tags">
      <div v-for="(value, key) in activeFilters" :key="key" class="filter-tag">
        {{ getFilterLabel(key, value) }}
        <span class="remove-tag" @click="removeFilter(key)">×</span>
      </div>
    </div>

    <!-- 筛选抽屉 -->
    <div v-show="showFilterOptions" class="filter-drawer-container">
      <div class="filter-drawer" :class="{ 'filter-drawer-open': showFilterOptions }">
        <div class="filter-options-header">
          <h3>筛选条件</h3>
          <span class="close-button" @click="closeFilterDrawer">×</span>
        </div>

        <!-- 性别筛选 -->
        <div class="filter-section">
          <h4>性别</h4>
          <div class="gender-options">
            <div v-for="option in genderOptions" :key="option.value"
              :class="['gender-option', { active: selectedGender === option.value }]"
              @click="selectedGender = option.value">
              {{ option.label }}
            </div>
          </div>
        </div>

        <!-- 年龄筛选 -->
        <div class="filter-section">
          <h4>年龄范围</h4>
          <div class="age-range">
            <div class="range-values">
              <span class="min-value">{{ ageRange[0] }}</span>
              <span class="range-separator">至</span>
              <span class="max-value">{{ ageRange[1] }}</span>
              <span>岁</span>
            </div>

            <div class="range-slider-container">
              <!-- 背景轨道 -->
              <div class="range-track" />

              <!-- 填充部分 -->
              <div class="range-fill" :style="{
                left: ((ageRange[0] - 18) / 42) * 100 + '%',
                width: ((ageRange[1] - ageRange[0]) / 42) * 100 + '%'
              }" />

              <!-- 最小值滑块 -->
              <div class="thumb min-thumb" :style="{ left: ((ageRange[0] - 18) / 42) * 100 + '%' }"
                @touchstart="startDrag('min')" @mousedown.prevent="startDrag('min')" />

              <!-- 最大值滑块 -->
              <div class="thumb max-thumb" :style="{ left: ((ageRange[1] - 18) / 42) * 100 + '%' }"
                @touchstart="startDrag('max')" @mousedown.prevent="startDrag('max')" />
            </div>
          </div>
        </div>

        <!-- 地区筛选 -->
        <div class="filter-section">
          <h4>地区</h4>
                  <div class="province-options">
          <div v-for="province in provinces" :key="province.code"
            :class="['province-option', { active: selectedProvinceCode === province.code }]" @click="selectedProvinceCode = province.code">
            {{ province.name }}
          </div>
        </div>
        </div>

        <!-- 兴趣标签筛选 -->
        <div class="filter-section">
          <h4>兴趣标签</h4>
          <div class="tag-options">
            <div v-for="tag in tags" :key="tag" :class="['tag-option', { active: selectedTags.includes(tag) }]"
              @click="toggleTag(tag)">
              {{ tag }}
            </div>
          </div>
        </div>

        <!-- 确认按钮 -->
        <div class="filter-action">
          <button class="reset-button" @click="resetFilters">重置</button>
          <button class="apply-button" @click="applyFilters">应用筛选</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'

// 筛选面板显示状态
const showFilterOptions = ref(false)

// 性别筛选选项
const genderOptions = [
  { label: '全部', value: '' },
  { label: '男', value: 'male' },
  { label: '女', value: 'female' }
]
const selectedGender = ref('')

// 年龄范围筛选
const ageRange = ref([20, 30])
const minAge = 18
const maxAge = 60
const rangeWidth = maxAge - minAge

// 拖拽相关变量
const isDragging = ref(false)
const currentThumb = ref(null) // 'min' 或 'max'

// 开始拖拽
const startDrag = thumb => {
  isDragging.value = true
  currentThumb.value = thumb

  // 添加全局事件监听
  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('touchmove', handleDrag, { passive: false })
  document.addEventListener('mouseup', stopDrag)
  document.addEventListener('touchend', stopDrag)
}

// 处理拖拽
const handleDrag = e => {
  if (!isDragging.value) {
    return
  }

  // 对于触摸事件，阻止页面滚动
  if (e.type === 'touchmove') {
    e.preventDefault()
  }

  // 获取滑块容器
  const container = document.querySelector('.range-slider-container')
  if (!container) {
    return
  }

  // 获取容器位置和宽度
  const rect = container.getBoundingClientRect()
  const containerWidth = rect.width

  // 获取鼠标/触摸位置
  const clientX = e.clientX || (e.touches && e.touches[0].clientX)
  if (clientX === undefined) {
    return
  }

  // 计算相对位置 (0 到 1)
  let position = (clientX - rect.left) / containerWidth
  position = Math.max(0, Math.min(1, position))

  // 计算对应的年龄值
  const age = Math.round(position * rangeWidth + minAge)

  // 更新对应的滑块值，实现碰撞效果
  if (currentThumb.value === 'min') {
    // 最小值不能超过最大值
    ageRange.value[0] = Math.min(age, ageRange.value[1])
  } else {
    // 最大值不能小于最小值
    ageRange.value[1] = Math.max(age, ageRange.value[0])
  }
}

// 停止拖拽
const stopDrag = () => {
  isDragging.value = false

  // 移除全局事件监听
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('touchmove', handleDrag, { passive: false })
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('touchend', stopDrag)
}

// 组件卸载时清除事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('touchmove', handleDrag, { passive: false })
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('touchend', stopDrag)
})

// 地区选项
const provinces = [
  { code: '', name: '全部' },
  { code: '11', name: '北京市' },
  { code: '31', name: '上海市' },
  { code: '44', name: '广东省' },
  { code: '33', name: '浙江省' },
  { code: '51', name: '四川省' }
]
const selectedProvinceCode = ref('')

// 兴趣标签
const tags = [
  '旅行',
  '摄影',
  '美食',
  '音乐',
  '电影',
  '健身',
  '阅读',
  '绘画',
  '设计',
  '篮球',
  '舞蹈',
  '烹饪'
]
const selectedTags = ref([])

// 当前激活的筛选条件
const activeFilters = reactive({})

// 初始化筛选条件
onMounted(() => {
  // 如果有传入的初始筛选条件，可以在这里设置
  initializeFilters()
})

// 初始化筛选条件
const initializeFilters = () => {
  // 根据activeFilters设置当前筛选值
  if (activeFilters.gender) {
    selectedGender.value = activeFilters.gender
  }

  if (activeFilters.age) {
    ageRange.value = [...activeFilters.age]
  }

      if (activeFilters.provinceCode) {
      selectedProvinceCode.value = activeFilters.provinceCode
    }

  if (activeFilters.tags) {
    selectedTags.value = [...activeFilters.tags]
  }
}

// 打开筛选抽屉
const toggleFilterDrawer = () => {
  showFilterOptions.value = !showFilterOptions.value
  if (showFilterOptions.value) {
    document.body.style.overflow = 'hidden' // 防止背景滚动
    // 打开时初始化筛选条件
    initializeFilters()
  }
}

// 关闭筛选抽屉
const closeFilterDrawer = () => {
  showFilterOptions.value = false
  document.body.style.overflow = '' // 恢复背景滚动
}

// 切换标签选择状态
const toggleTag = tag => {
  const index = selectedTags.value.indexOf(tag)
  if (index > -1) {
    selectedTags.value.splice(index, 1)
  } else {
    selectedTags.value.push(tag)
  }
}

// 筛选条件配置
const filterConfigs = [
  {
    key: 'gender',
    getValue: () => selectedGender.value,
    isActive: (value) => !!value
  },
  {
    key: 'age',
    getValue: () => [ageRange.value[0], ageRange.value[1]],
    isActive: (value) => value[0] !== minAge || value[1] !== maxAge
  },
  {
    key: 'provinceCode',
    getValue: () => selectedProvinceCode.value,
    isActive: (value) => value !== ''
  },
  {
    key: 'tags',
    getValue: () => [...selectedTags.value],
    isActive: (value) => value.length > 0
  }
]

// 应用筛选条件
const applyFilters = () => {
  // 清空现有筛选条件
  Object.keys(activeFilters).forEach(key => delete activeFilters[key])

  // 应用新的筛选条件
  filterConfigs.forEach(config => {
    const value = config.getValue()
    if (config.isActive(value)) {
      activeFilters[config.key] = value
    }
  })

  emit('filter', { ...activeFilters })
  closeFilterDrawer()
}

// 重置筛选条件
const resetFilters = () => {
  // 重置所有筛选选项
  selectedGender.value = ''
  ageRange.value = [minAge, maxAge]
  selectedRegion.value = '全部'
  selectedTags.value = []

  // 清空激活的筛选条件
  Object.keys(activeFilters).forEach(key => {
    delete activeFilters[key]
  })
}

// 移除特定筛选条件
const removeFilter = key => {
  delete activeFilters[key]

  // 同时重置对应的筛选选项
  if (key === 'gender') {
    selectedGender.value = ''
  } else if (key === 'age') {
    ageRange.value = [minAge, maxAge]
      } else if (key === 'provinceCode') {
      selectedProvinceCode.value = ''
  } else if (key === 'tags') {
    selectedTags.value = []
  }

  // 触发筛选事件
  emit('filter', { ...activeFilters })
}

// 获取筛选标签显示文本
const getFilterLabel = (key, value) => {
  if (key === 'gender') {
    return value === 'male' ? '男' : '女'
  } else if (key === 'age') {
    return `${value[0]}-${value[1]}岁`
  } else if (key === 'provinceCode') {
    const province = provinces.find(p => p.code === value)
    return province ? province.name : value
  } else if (key === 'tags') {
    // 如果标签太多，只显示前两个
    if (value.length > 2) {
      return `标签: ${value.slice(0, 2).join(',')}等${value.length}个`
    }
    return `标签: ${value.join(',')}`
  }
  return ''
}

// 定义组件事件
const emit = defineEmits(['filter'])
</script>

<style scoped>
.filter-bar {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 15px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  z-index: 50;
}

.filter-button {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background: #f5f5f5;
  border-radius: 20px;
  margin-right: 10px;
  cursor: pointer;
  white-space: nowrap;
  transition: background-color 0.2s ease;
}

.filter-button:hover {
  background: #eee;
}

.filter-button:active {
  background: #e0e0e0;
}

.filter-icon {
  margin-right: 5px;
  font-size: 14px;
  color: #666;
}

.filter-tags {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
  flex: 1;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
  padding: 4px 0;
}

.filter-tags::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari, and Opera */
}

.filter-tag {
  display: flex;
  align-items: center;
  padding: 4px 10px;
  background: #ff6b6b15;
  color: #ff6b6b;
  border-radius: 15px;
  font-size: 12px;
  margin-right: 8px;
  white-space: nowrap;
  border: 1px solid rgba(255, 107, 107, 0.2);
}

.remove-tag {
  margin-left: 5px;
  cursor: pointer;
  font-weight: bold;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 107, 107, 0.1);
}

.remove-tag:hover {
  background: rgba(255, 107, 107, 0.2);
}

/* 抽屉式筛选弹窗 */
.filter-drawer-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: transparent;
  z-index: 1000;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.filter-drawer {
  background: #fff;
  width: 100%;
  max-height: 80vh;
  border-radius: 0 0 20px 20px;
  padding: 15px;
  position: relative;
  overflow-y: auto;
  transform: translateY(-20px);
  opacity: 0;
  transition:
    transform 0.3s ease,
    opacity 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.filter-drawer-open {
  transform: translateY(0);
  opacity: 1;
}

.drawer-handle {
  width: 40px;
  height: 5px;
  background: #e0e0e0;
  border-radius: 3px;
  margin: 0 auto 15px;
}

.filter-options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.filter-options-header h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.close-button {
  font-size: 22px;
  cursor: pointer;
  color: #999;
  padding: 0 5px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background-color: #f5f5f5;
}

.filter-section {
  margin-bottom: 20px;
}

.filter-section h4 {
  margin: 0 0 12px;
  font-size: 14px;
  color: #666;
  font-weight: normal;
}

/* 性别选项样式 */
.gender-options {
  display: flex;
  gap: 10px;
  margin-bottom: 5px;
}

.gender-option {
  padding: 8px 20px;
  border-radius: 20px;
  background: #f5f5f5;
  font-size: 14px;
  cursor: pointer;
  text-align: center;
  flex: 1;
  transition: all 0.2s ease;
}

.gender-option:hover {
  background: #eee;
}

.gender-option.active {
  background: #ff6b6b;
  color: white;
  box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3);
}

.age-range {
  padding: 10px 5px;
  margin: 10px 0;
}

.range-values {
  text-align: center;
  margin-bottom: 15px;
  font-size: 16px;
  color: #333;
}

.min-value,
.max-value {
  font-weight: 500;
  color: #ff6b6b;
}

.range-separator {
  margin: 0 8px;
  color: #666;
}

.range-slider-container {
  position: relative;
  height: 40px;
  padding: 0;
  margin: 20px 10px;
}

.range-track {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 4px;
  transform: translateY(-50%);
  background: #e0e0e0;
  border-radius: 2px;
}

.range-fill {
  position: absolute;
  top: 50%;
  height: 4px;
  transform: translateY(-50%);
  background: #ff6b6b;
  border-radius: 2px;
}

/* 滑块样式 */
.thumb {
  position: absolute;
  top: 50%;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #fff;
  border: 2px solid #ff6b6b;
  transform: translate(-50%, -50%);
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  z-index: 2;
  touch-action: none;
  transition:
    transform 0.15s ease,
    box-shadow 0.15s ease;
}

.min-thumb {
  z-index: 3;
}

.max-thumb {
  z-index: 4;
}

.thumb:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  transform: translate(-50%, -50%) scale(1.1);
}

.thumb:active {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
  transform: translate(-50%, -50%) scale(1.15);
}

.thumb::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 44px;
  height: 44px;
  border-radius: 50%;
  z-index: -1;
}

.filter-action {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.apply-button,
.reset-button {
  padding: 10px 25px;
  border-radius: 20px;
  border: none;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.apply-button {
  background: #ff6b6b;
  color: white;
  flex: 2;
  box-shadow: 0 2px 6px rgba(255, 107, 107, 0.3);
}

.apply-button:hover {
  background: #ff5252;
  box-shadow: 0 3px 8px rgba(255, 107, 107, 0.4);
}

.reset-button {
  background: #f5f5f5;
  color: #666;
  flex: 1;
  margin-right: 10px;
}

.reset-button:hover {
  background: #e0e0e0;
}

.province-options,
.tag-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.province-option,
.tag-option {
  padding: 6px 12px;
  border-radius: 16px;
  background: #f5f5f5;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.province-option:hover,
.tag-option:hover {
  background: #eee;
}

.province-option.active,
.tag-option.active {
  background: #ff6b6b;
  color: white;
  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
}
</style>
