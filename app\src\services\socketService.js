/**
 * Socket 服务 - WebSocket 真实连接实现
 */

// 事件监听器
const listeners = {
  message: [],
  connect: [],
  disconnect: [],
  error: [],
  reconnect: []
}

// 连接状态和配置
let socket = null
let isConnected = false
let reconnectAttempts = 0
let maxReconnectAttempts = 5
let reconnectInterval = 3000
let reconnectTimer = null
let serverUrl = ''
let authToken = ''
let debugMode = false

// WebSocket 服务
const socketService = {
  // 配置服务器地址
  configure(config = {}) {
    serverUrl = config.url || import.meta.env.VITE_WS_URL || 'ws://localhost:3000/ws'
    maxReconnectAttempts = config.maxReconnectAttempts || 5
    reconnectInterval = config.reconnectInterval || 3000
    debugMode = config.debug || false
    return this
  },

  // 连接到服务器
  connect(token) {
    if (debugMode) console.log('Socket: 尝试连接到服务器', serverUrl)
    
    if (token) {
      authToken = token
    }

    try {
      // 如果已经连接，先断开
      if (socket && socket.readyState === WebSocket.OPEN) {
        this.disconnect()
      }

      // 创建 WebSocket 连接
      const wsUrl = authToken ? `${serverUrl}?token=${authToken}` : serverUrl
      socket = new WebSocket(wsUrl)

      // 连接成功
      socket.onopen = () => {
        if (debugMode) console.log('Socket: 连接成功')
        isConnected = true
        reconnectAttempts = 0
        
        // 清除重连定时器
        if (reconnectTimer) {
          clearTimeout(reconnectTimer)
          reconnectTimer = null
        }

        // 触发连接成功事件
        listeners.connect.forEach(callback => {
          try {
            callback()
          } catch (error) {
            console.error('Socket: 连接事件回调错误', error)
          }
        })
      }

      // 接收消息
      socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          if (debugMode) console.log('Socket: 收到消息', data)

          // 触发消息事件
          listeners.message.forEach(callback => {
            try {
              callback(data)
            } catch (error) {
              console.error('Socket: 消息事件回调错误', error)
            }
          })
        } catch (error) {
          console.error('Socket: 消息解析错误', error)
        }
      }

      // 连接关闭
      socket.onclose = (event) => {
        if (debugMode) console.log('Socket: 连接关闭', event.code, event.reason)
        isConnected = false

        // 触发断开连接事件
        listeners.disconnect.forEach(callback => {
          try {
            callback({ code: event.code, reason: event.reason })
          } catch (error) {
            console.error('Socket: 断开连接事件回调错误', error)
          }
        })

        // 如果不是手动关闭，尝试重连
        if (event.code !== 1000 && reconnectAttempts < maxReconnectAttempts) {
          this._attemptReconnect()
        }
      }

      // 连接错误
      socket.onerror = (error) => {
        console.error('Socket: 连接错误', error)
        isConnected = false

        // 触发错误事件
        listeners.error.forEach(callback => {
          try {
            callback(error)
          } catch (callbackError) {
            console.error('Socket: 错误事件回调错误', callbackError)
          }
        })
      }

    } catch (error) {
      console.error('Socket: 创建连接失败', error)
      
      // 触发错误事件
      listeners.error.forEach(callback => {
        try {
          callback(error)
        } catch (callbackError) {
          console.error('Socket: 错误事件回调错误', callbackError)
        }
      })
    }

    return this
  },

  // 断开连接
  disconnect() {
    if (debugMode) console.log('Socket: 手动断开连接')
    
    // 清除重连定时器
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }

    // 重置重连次数
    reconnectAttempts = maxReconnectAttempts

    if (socket) {
      // 手动关闭，使用 1000 状态码
      socket.close(1000, '手动断开连接')
      socket = null
    }

    isConnected = false
    return this
  },

  // 发送消息
  sendMessage(data) {
    if (!isConnected || !socket || socket.readyState !== WebSocket.OPEN) {
      if (debugMode) console.error('Socket: 未连接，无法发送消息')
      return false
    }

    try {
      const message = typeof data === 'string' ? data : JSON.stringify(data)
      socket.send(message)
      if (debugMode) console.log('Socket: 发送消息', data)
      return true
    } catch (error) {
      console.error('Socket: 发送消息失败', error)
      return false
    }
  },

  // 添加事件监听器
  on(event, callback) {
    if (!listeners[event]) {
      listeners[event] = []
    }

    listeners[event].push(callback)
    return this
  },

  // 移除事件监听器
  off(event, callback) {
    if (listeners[event]) {
      listeners[event] = listeners[event].filter(cb => cb !== callback)
    }
    return this
  },

  // 移除所有事件监听器
  removeAllListeners(event) {
    if (event && listeners[event]) {
      listeners[event] = []
    } else if (!event) {
      Object.keys(listeners).forEach(key => {
        listeners[key] = []
      })
    }
    return this
  },

  // 获取连接状态
  isConnected() {
    return isConnected && socket && socket.readyState === WebSocket.OPEN
  },

  // 获取 WebSocket 状态
  getReadyState() {
    if (!socket) return WebSocket.CLOSED
    return socket.readyState
  },

  // 获取重连状态
  getReconnectInfo() {
    return {
      attempts: reconnectAttempts,
      maxAttempts: maxReconnectAttempts,
      isReconnecting: !!reconnectTimer
    }
  },

  // 私有方法：尝试重连
  _attemptReconnect() {
    if (reconnectAttempts >= maxReconnectAttempts) {
      if (debugMode) console.log('Socket: 重连次数已达上限，停止重连')
      return
    }

    reconnectAttempts++
    if (debugMode) console.log(`Socket: 尝试第 ${reconnectAttempts} 次重连...`)

    // 触发重连事件
    listeners.reconnect.forEach(callback => {
      try {
        callback({ attempt: reconnectAttempts, maxAttempts: maxReconnectAttempts })
      } catch (error) {
        console.error('Socket: 重连事件回调错误', error)
      }
    })

    reconnectTimer = setTimeout(() => {
      this.connect(authToken)
    }, reconnectInterval)
  },

  // 模拟接收消息（保留用于测试）
  simulateIncomingMessage(message) {
    if (!this.isConnected()) {
      if (debugMode) console.error('Socket: 未连接，无法模拟接收消息')
      return
    }

    if (debugMode) console.log('Socket: 模拟收到消息', message)

    // 触发消息事件
    listeners.message.forEach(callback => {
      try {
        callback(message)
      } catch (error) {
        console.error('Socket: 模拟消息事件回调错误', error)
      }
    })
  }
}

export default socketService
