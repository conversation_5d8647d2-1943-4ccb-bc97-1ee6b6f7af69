<template>
  <page-layout :title="`聊天${totalUnreadCount > 0 ? ` (${formatUnreadCount(totalUnreadCount)})` : ''}`">
    <!-- 顶部工具栏 -->
    <div class="message-header">
      <div class="header-actions">
        <button
          class="action-btn search-btn"
          :class="{ active: showSearchBar }"
          @click="toggleSearchBar"
          :disabled="componentState.isLoading"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="11" cy="11" r="8"/>
            <path d="m21 21-4.35-4.35"/>
          </svg>
        </button>

        <button
          class="action-btn refresh-btn"
          :class="{ loading: componentState.isRefreshing }"
          @click="refreshData"
          :disabled="componentState.isLoading || componentState.isRefreshing"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="23 4 23 10 17 10"/>
            <polyline points="1 20 1 14 7 14"/>
            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
          </svg>
        </button>

        <button
          v-if="totalUnreadCount > 0"
          class="action-btn mark-read-btn"
          @click="markAllAsRead"
          :disabled="componentState.isLoading"
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="20 6 9 17 4 12"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div v-if="showSearchBar" class="search-container">
      <div class="search-wrapper">
        <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
        <input
          v-model="searchInput"
          class="search-input"
          type="text"
          placeholder="搜索联系人或消息内容..."
          maxlength="50"
          @input="handleSearch($event.target.value)"
        />
        <button v-if="searchInput" class="clear-search-btn" @click="clearSearch">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- 筛选栏 -->
    <div v-if="!showSearchBar" class="filter-container">
      <div class="filter-tabs">
        <button
          class="filter-tab"
          :class="{ active: componentState.selectedFilter === 'all' }"
          @click="setFilter('all')"
        >
          全部
          <span v-if="filteredContacts.length > 0" class="tab-count">({{ filteredContacts.length }})</span>
        </button>
        <button
          class="filter-tab"
          :class="{ active: componentState.selectedFilter === 'unread' }"
          @click="setFilter('unread')"
        >
          未读
          <span v-if="totalUnreadCount > 0" class="tab-count unread">({{ formatUnreadCount(totalUnreadCount) }})</span>
        </button>
        <button
          class="filter-tab"
          :class="{ active: componentState.selectedFilter === 'recent' }"
          @click="setFilter('recent')"
        >
          最近
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="componentState.isLoading" class="loading-container">
      <div class="loading-spinner">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M21 12a9 9 0 11-6.219-8.56"/>
        </svg>
      </div>
      <p class="loading-text">加载中...</p>
    </div>

    <!-- 匹配部分 -->
    <div v-if="!componentState.isLoading && !componentState.isSearching" class="match-container">
      <h2 class="section-title">
        新的匹配
        <span v-if="filteredMatches.length > 0" class="section-count">({{ filteredMatches.length }})</span>
      </h2>

      <!-- 空状态 -->
      <empty-state v-if="filteredMatches.length === 0" icon="🔍" text="暂无新匹配" />

      <!-- 匹配列表 -->
      <div v-else class="match-list">
        <user-card
          v-for="(match, index) in filteredMatches"
          :key="`match-${match.userId || match.id || index}`"
          type="match"
          :username="match.nickname || match.username"
          :avatar="match.avatar"
          :is-new="match.isNew"
          @click="viewProfile(match.userId || match.id)"
        />
      </div>
    </div>

    <!-- 联系人部分 -->
    <div v-if="!componentState.isLoading" class="contact-container">
      <h2 class="section-title">
        {{ componentState.isSearching ? '搜索结果' : '联系人' }}
        <span v-if="filteredContacts.length > 0" class="section-count">({{ filteredContacts.length }})</span>
      </h2>

      <!-- 搜索无结果 -->
      <empty-state
        v-if="componentState.isSearching && filteredContacts.length === 0"
        icon="🔍"
        :text="`未找到包含「${searchInput}」的联系人或消息`"
      />

      <!-- 筛选无结果 -->
      <empty-state
        v-else-if="!componentState.isSearching && filteredContacts.length === 0 && componentState.selectedFilter !== 'all'"
        icon="📭"
        :text="getEmptyFilterText()"
      />

      <!-- 完全无联系人 -->
      <empty-state
        v-else-if="!componentState.isSearching && messageStore.contacts.length === 0"
        icon="📭"
        text="暂无联系人"
      />

      <!-- 联系人列表 -->
      <div v-else class="contact-list">
        <user-card
          v-for="(contact, index) in filteredContacts"
          :key="`contact-${contact.id || index}`"
          type="contact"
          :username="contact.nickname || contact.username"
          :avatar="contact.avatar"
          :last-message="contact.lastMessage?.content"
          :last-message-time="formatTime(contact.lastMessage?.time || contact.lastMessageTime)"
          :unread-count="contact.unreadCount"
          @click="goToChat(contact.id)"
          @long-press="handleLongPress(contact)"
        />
      </div>
    </div>

    <!-- 全局空状态 -->
    <div v-if="showEmptyState" class="global-empty-state">
      <empty-state
        icon="💬"
        text="还没有任何聊天记录"
        description="开始匹配并与其他用户聊天吧！"
      />
      <div class="empty-actions">
        <button class="btn btn-primary" @click="router.push('/')">
          开始匹配
        </button>
      </div>
    </div>

    <!-- Socket连接状态指示器 -->
    <div v-if="!messageStore.socketConnected" class="connection-status">
      <div class="status-indicator offline">
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="3"/>
        </svg>
        <span>连接已断开</span>
      </div>
    </div>
  </page-layout>
</template>

<script setup>
import { ref, computed, onMounted, onActivated, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import PageLayout from '../components/PageLayout.vue'
import EmptyState from '../components/EmptyState.vue'
import UserCard from '../components/UserCard.vue'
import { useMessageStore } from '../stores/messageStore'
import { useUserStore } from '../stores/userStore'

const router = useRouter()
const messageStore = useMessageStore()
const userStore = useUserStore()

// 组件状态
const componentState = ref({
  isLoading: false,
  isRefreshing: false,
  lastRefreshTime: null,
  searchQuery: '',
  selectedFilter: 'all', // all, unread, recent
  isSearching: false
})

// 搜索和筛选相关
const searchInput = ref('')
const showSearchBar = ref(false)

// 计算属性 - 筛选后的匹配列表
const filteredMatches = computed(() => {
  let matches = messageStore.matches || []

  if (searchInput.value.trim()) {
    const query = searchInput.value.toLowerCase().trim()
    matches = matches.filter(match => {
      const username = (match.nickname || match.username || '').toLowerCase()
      return username.includes(query)
    })
  }

  return matches
})

// 计算属性 - 筛选后的联系人列表
const filteredContacts = computed(() => {
  let contacts = messageStore.contacts || []

  // 搜索筛选
  if (searchInput.value.trim()) {
    const query = searchInput.value.toLowerCase().trim()
    contacts = contacts.filter(contact => {
      const username = (contact.nickname || contact.username || '').toLowerCase()
      const lastMessage = (contact.lastMessage?.content || '').toLowerCase()
      return username.includes(query) || lastMessage.includes(query)
    })
  }

  // 状态筛选
  switch (componentState.value.selectedFilter) {
    case 'unread':
      contacts = contacts.filter(contact => contact.unreadCount > 0)
      break
    case 'recent':
      contacts = contacts.filter(contact => {
        const lastTime = contact.lastMessage?.time || contact.lastMessageTime
        if (!lastTime) return false
        const timeDiff = Date.now() - (typeof lastTime === 'string' ? new Date(lastTime).getTime() : lastTime)
        return timeDiff < 24 * 60 * 60 * 1000 // 24小时内
      })
      break
    default:
      // 'all' - 不筛选
      break
  }

  // 按最后消息时间排序
  return contacts.sort((a, b) => {
    const timeA = a.lastMessage?.time || a.lastMessageTime || 0
    const timeB = b.lastMessage?.time || b.lastMessageTime || 0
    const timestampA = typeof timeA === 'string' ? new Date(timeA).getTime() : timeA
    const timestampB = typeof timeB === 'string' ? new Date(timeB).getTime() : timeB
    return timestampB - timestampA
  })
})

// 计算属性 - 总未读消息数
const totalUnreadCount = computed(() => {
  return messageStore.totalUnreadCount || 0
})

// 计算属性 - 是否显示空状态
const showEmptyState = computed(() => {
  return !componentState.value.isLoading &&
         filteredMatches.value.length === 0 &&
         filteredContacts.value.length === 0
})

// 初始化数据的公共方法
const initializeMessageData = async () => {
  if (componentState.value.isLoading) {
    console.log('📱 Message页面正在加载中，跳过重复初始化')
    return
  }

  componentState.value.isLoading = true

  try {
    console.log('📱 Message页面初始化数据')

    // 清除当前聊天联系人状态（确保不会误判为在聊天页面）
    messageStore.clearCurrentChatContact()

    // 强制刷新联系人数据，确保显示最新状态
    await messageStore.forceRefreshContacts()

    // 加载匹配数据
    await messageStore.loadMatches()

    componentState.value.lastRefreshTime = Date.now()

    console.log('📱 Message页面数据加载完成:', {
      contacts: messageStore.contacts.length,
      matches: messageStore.matches.length,
      unreadCount: totalUnreadCount.value
    })

  } catch (error) {
    console.error('📱 Message页面数据初始化失败:', error)
    // 可以在这里添加错误提示
  } finally {
    componentState.value.isLoading = false
  }
}

// 刷新数据
const refreshData = async () => {
  if (componentState.value.isRefreshing) {
    console.log('📱 正在刷新中，跳过重复刷新')
    return
  }

  componentState.value.isRefreshing = true

  try {
    console.log('📱 手动刷新Message页面数据')
    await initializeMessageData()
  } catch (error) {
    console.error('📱 刷新数据失败:', error)
  } finally {
    componentState.value.isRefreshing = false
  }
}

// 搜索功能
const handleSearch = (query) => {
  componentState.value.isSearching = !!query.trim()
  searchInput.value = query
}

const clearSearch = () => {
  searchInput.value = ''
  componentState.value.isSearching = false
  showSearchBar.value = false
}

const toggleSearchBar = () => {
  showSearchBar.value = !showSearchBar.value
  if (!showSearchBar.value) {
    clearSearch()
  } else {
    // 聚焦搜索框
    nextTick(() => {
      const searchInputEl = document.querySelector('.search-input')
      if (searchInputEl) {
        searchInputEl.focus()
      }
    })
  }
}

// 筛选功能
const setFilter = (filter) => {
  componentState.value.selectedFilter = filter
  console.log('📱 设置筛选条件:', filter)
}

// 页面挂载时初始化数据
onMounted(async () => {
  console.log('📱 Message组件已挂载')
  await initializeMessageData()
})

// 页面重新激活时刷新数据（解决从其他页面返回时的数据同步问题）
onActivated(async () => {
  console.log('📱 Message页面重新激活，检查是否需要刷新数据')

  // 检查是否需要刷新（距离上次刷新超过30秒）
  const now = Date.now()
  const shouldRefresh = !componentState.value.lastRefreshTime ||
                       (now - componentState.value.lastRefreshTime) > 30000

  if (shouldRefresh) {
    console.log('📱 需要刷新数据')
    await initializeMessageData()
  } else {
    console.log('📱 数据较新，无需刷新')
  }
})

// 组件卸载时清理
onUnmounted(() => {
  console.log('📱 Message组件即将卸载')
  // 清理搜索状态
  clearSearch()
})

// 监听联系人数据变化
watch(() => messageStore.contacts, (newContacts, oldContacts) => {
  const newCount = newContacts?.length || 0
  const oldCount = oldContacts?.length || 0

  if (newCount !== oldCount) {
    console.log('📱 联系人数据已更新:', newCount, '个联系人')
  }
}, { deep: true })

// 监听未读消息数变化
watch(totalUnreadCount, (newCount, oldCount) => {
  if (newCount !== oldCount) {
    console.log('📱 未读消息数变化:', oldCount, '->', newCount)

    // 更新页面标题显示未读数
    if (newCount > 0) {
      document.title = `(${newCount}) 聊天 - 社交匹配`
    } else {
      document.title = '聊天 - 社交匹配'
    }
  }
})

// 监听Socket连接状态
watch(() => messageStore.socketConnected, (connected) => {
  console.log('📱 Socket连接状态变化:', connected ? '已连接' : '已断开')

  if (connected) {
    // Socket重连后刷新数据
    console.log('📱 Socket重连，刷新数据')
    nextTick(() => {
      refreshData()
    })
  }
})

// 格式化时间 - 增强版本
const formatTime = (timestamp) => {
  if (!timestamp) return ''

  try {
    const now = Date.now()
    const time = typeof timestamp === 'string' ? new Date(timestamp).getTime() : timestamp

    // 验证时间戳有效性
    if (isNaN(time) || time <= 0) {
      return ''
    }

    const diff = now - time

    // 小于1分钟
    if (diff < 60000) {
      return '刚刚'
    }

    // 小于1小时
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000)
      return `${minutes}分钟前`
    }

    // 小于24小时
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000)
      return `${hours}小时前`
    }

    // 小于7天
    if (diff < 604800000) {
      const days = Math.floor(diff / 86400000)
      return `${days}天前`
    }

    // 小于30天，显示星期
    if (diff < 2592000000) {
      const date = new Date(time)
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      return weekdays[date.getDay()]
    }

    // 大于30天，显示具体日期
    const date = new Date(time)
    const currentYear = new Date().getFullYear()
    const messageYear = date.getFullYear()

    if (currentYear === messageYear) {
      return `${date.getMonth() + 1}月${date.getDate()}日`
    } else {
      return `${messageYear}年${date.getMonth() + 1}月${date.getDate()}日`
    }
  } catch (error) {
    console.error('时间格式化错误:', error)
    return ''
  }
}

// 格式化未读消息数
const formatUnreadCount = (count) => {
  if (!count || count <= 0) return ''
  if (count > 99) return '99+'
  return count.toString()
}

// 跳转到聊天页面 - 增强版本
const goToChat = (id) => {
  if (!id) {
    console.error('📱 无效的联系人ID:', id)
    return
  }

  try {
    console.log('📱 跳转到聊天页面:', id)
    router.push(`/chat/${id}`)
  } catch (error) {
    console.error('📱 跳转聊天页面失败:', error)
  }
}

// 查看用户资料 - 增强版本
const viewProfile = (id) => {
  if (!id) {
    console.error('📱 无效的用户ID:', id)
    return
  }

  try {
    console.log('📱 查看用户资料:', id)
    router.push(`/profile/${id}`)
  } catch (error) {
    console.error('📱 跳转用户资料页面失败:', error)
  }
}

// 长按删除联系人
const handleLongPress = (contact) => {
  console.log('📱 长按联系人:', contact)
  // 可以在这里添加删除联系人的逻辑
}

// 标记所有消息为已读
const markAllAsRead = async () => {
  try {
    console.log('📱 标记所有消息为已读')

    const unreadContacts = filteredContacts.value.filter(contact => contact.unreadCount > 0)

    for (const contact of unreadContacts) {
      await messageStore.markAsRead(contact.id)
    }

    console.log('📱 所有消息已标记为已读')
  } catch (error) {
    console.error('📱 标记已读失败:', error)
  }
}

// 获取筛选空状态文本
const getEmptyFilterText = () => {
  switch (componentState.value.selectedFilter) {
    case 'unread':
      return '暂无未读消息'
    case 'recent':
      return '暂无最近聊天'
    default:
      return '暂无联系人'
  }
}
</script>

<style scoped>
/* 消息页面头部 */
.message-header {
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  background: white;
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: #f8f9fa;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #e9ecef;
  color: var(--color-primary);
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn.active {
  background: var(--color-primary);
  color: white;
}

.action-btn.loading svg {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 搜索栏 */
.search-container {
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.search-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #999;
  z-index: 1;
}

.search-input {
  width: 100%;
  height: 44px;
  padding: 0 44px 0 40px;
  border: 1px solid #e0e0e0;
  border-radius: 22px;
  background: #f8f9fa;
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.search-input:focus {
  border-color: var(--color-primary);
  background: white;
  box-shadow: 0 0 0 3px rgba(255, 88, 100, 0.1);
}

.clear-search-btn {
  position: absolute;
  right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  border-radius: 50%;
  background: #ccc;
  color: white;
  cursor: pointer;
  transition: background 0.2s ease;
}

.clear-search-btn:hover {
  background: #999;
}

/* 筛选栏 */
.filter-container {
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.filter-tabs {
  display: flex;
  gap: 8px;
}

.filter-tab {
  padding: 8px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  background: white;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.filter-tab:hover {
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.filter-tab.active {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

.tab-count {
  font-size: 12px;
  opacity: 0.8;
}

.tab-count.unread {
  background: #ff4757;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: bold;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #999;
}

.loading-spinner svg {
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.loading-text {
  font-size: 14px;
  margin: 0;
}

/* 章节标题 */
.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  padding: 0 20px;
}

.section-count {
  font-size: 14px;
  color: #999;
  font-weight: normal;
}

/* 匹配和联系人容器 */
.match-container,
.contact-container {
  padding: 20px 0;
}

.match-list,
.contact-list {
  padding: 0 20px;
}

/* 全局空状态 */
.global-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  text-align: center;
}

.empty-actions {
  margin-top: 24px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  background: #e63946;
  transform: translateY(-1px);
}

/* 连接状态指示器 */
.connection-status {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-indicator.offline {
  background: #ff4757;
  color: white;
}

.status-indicator.offline svg {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .message-header {
    padding: 12px 16px;
  }

  .search-container,
  .filter-container {
    padding: 12px 16px;
  }

  .section-title {
    padding: 0 16px;
    font-size: 16px;
  }

  .match-list,
  .contact-list {
    padding: 0 16px;
  }

  .filter-tabs {
    gap: 6px;
  }

  .filter-tab {
    padding: 6px 12px;
    font-size: 13px;
  }

  .action-btn {
    width: 36px;
    height: 36px;
  }

  .global-empty-state {
    padding: 60px 16px;
  }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  .message-header,
  .search-container,
  .filter-container {
    background: #1a1a1a;
    border-color: #333;
  }

  .action-btn {
    background: #333;
    color: #ccc;
  }

  .action-btn:hover {
    background: #444;
  }

  .search-input {
    background: #333;
    border-color: #444;
    color: #ccc;
  }

  .search-input:focus {
    background: #444;
  }

  .filter-tab {
    background: #333;
    border-color: #444;
    color: #ccc;
  }

  .section-title {
    color: #ccc;
  }

  .loading-container {
    color: #666;
  }
}
</style>

<style scoped>
  /* Message页面特定样式 */
  .section-title {
    font-size: 15px;
    font-weight: 500;
    color: #333;
    margin: 10px 0 8px;
  }

  /* 匹配部分 */
  .match-container {
    margin-bottom: 15px;
    width: 100%;
  }

  .match-list {
    display: flex;
    overflow-x: auto;
    padding: 5px 0;
    -ms-overflow-style: none;
    scrollbar-width: none;
    width: 100%;
    gap: 12px;
  }

  .match-list::-webkit-scrollbar {
    display: none;
  }

  /* 联系人部分 */
  .contact-container {
    flex: 1;
    width: 100%;
  }

  .contact-list {
    background: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    width: 100%;
  }
</style>
