import { http, withError<PERSON><PERSON><PERSON> } from '../utils/request.js'

/**
 * 通用文件上传服务
 */
export const uploadService = {
  /**
   * 通用文件上传
   * @param {File} file - 要上传的文件
   * @param {string} type - 文件类型：'avatar'|'photo'|'message'|'post'
   * @returns {Promise}
   */
  uploadFile: withErrorHandler(
    async (file, type) => {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', type)
      
      return await http.upload('/upload', formData)
    },
    '文件上传失败'
  ),

  /**
   * 上传头像
   * @param {File} avatarFile - 头像文件
   * @returns {Promise}
   */
  uploadAvatar: withErrorHandler(
    async (avatarFile) => {
      console.log('📤 uploadService.uploadAvatar 开始:', {
        fileName: avatarFile.name,
        fileSize: avatarFile.size,
        fileType: avatarFile.type,
        isFile: avatarFile instanceof File
      })

      // 验证文件
      uploadService.validateFile(avatarFile, {
        allowedTypes: ['image/jpeg', 'image/jpg', 'image/png'],
        maxSize: 5 * 1024 * 1024 // 5MB
      })

      const formData = new FormData()
      formData.append('avatar', avatarFile)

      console.log('📤 FormData 创建完成，准备发送到 /user/avatar')
      console.log('📤 FormData 内容检查:', {
        hasAvatar: formData.has('avatar'),
        avatarValue: formData.get('avatar'),
        isAvatarFile: formData.get('avatar') instanceof File,
        avatarFileName: formData.get('avatar')?.name
      })

      const result = await http.upload('/user/avatar', formData)
      console.log('📤 uploadService.uploadAvatar 成功:', result)
      return result
    },
    '上传头像失败'
  ),

  /**
   * 上传相册（支持多张）
   * @param {File[]} photoFiles - 相册文件数组
   * @returns {Promise}
   */
  uploadPhotos: withErrorHandler(
    async (photoFiles) => {
      // 验证文件数量
      if (photoFiles.length > 6) {
        throw new Error('最多只能上传6张照片')
      }

      // 验证每个文件
      photoFiles.forEach(file => {
        uploadService.validateFile(file, {
          allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
          maxSize: 10 * 1024 * 1024 // 10MB
        })
      })

      const formData = new FormData()
      photoFiles.forEach(file => {
        formData.append('photos', file)
      })
      
      return await http.upload('/user/photos', formData)
    },
    '上传相册失败'
  ),

  /**
   * 上传聊天图片
   * @param {File} imageFile - 图片文件
   * @returns {Promise}
   */
  uploadChatImage: withErrorHandler(
    async (imageFile) => {
      // 验证文件
      uploadService.validateFile(imageFile, {
        allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
        maxSize: 10 * 1024 * 1024 // 10MB
      })

      const formData = new FormData()
      formData.append('image', imageFile)
      
      return await http.upload('/messages/upload', formData)
    },
    '上传图片失败'
  ),

  /**
   * 批量上传文件
   * @param {File[]} files - 要上传的文件数组
   * @param {string} type - 文件类型
   * @returns {Promise}
   */
  uploadFiles: withErrorHandler(
    async (files, type) => {
      const uploadPromises = files.map(file => uploadService.uploadFile(file, type))
      const results = await Promise.all(uploadPromises)
      return results
  },
    '批量上传失败'
  ),

  /**
   * 检查文件类型和大小
   * @param {File} file - 要检查的文件
   * @param {Object} options - 检查选项
   * @param {string[]} options.allowedTypes - 允许的文件类型
   * @param {number} options.maxSize - 最大文件大小（字节）
   * @returns {boolean}
   */
  validateFile(file, options = {}) {
    const {
      allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
      maxSize = 10 * 1024 * 1024 // 10MB
    } = options

    // 检查文件类型
    if (!allowedTypes.includes(file.type)) {
      throw new Error(`不支持的文件类型，仅支持：${allowedTypes.join(', ')}`)
    }

    // 检查文件大小
    if (file.size > maxSize) {
      const maxSizeMB = (maxSize / (1024 * 1024)).toFixed(1)
      throw new Error(`文件过大，最大支持 ${maxSizeMB}MB`)
    }

    return true
  }
} 