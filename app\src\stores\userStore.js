import { defineStore } from 'pinia'
import { reactive } from 'vue'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: reactive(JSON.parse(localStorage.getItem('userInfo') || '{}'))
  }),
  getters: {
    isLogin: state => !!state.token
  },
  actions: {
    setToken(token) {
      this.token = token
      localStorage.setItem('token', token)
    },
    setUserInfo(info) {
      // 确保响应式更新
      Object.keys(this.userInfo).forEach(key => {
        delete this.userInfo[key]
      })
      Object.assign(this.userInfo, info)
      localStorage.setItem('userInfo', JSON.stringify(info))
    },
    async logout() {
      try {
        // 调用登出API
        const { authService } = await import('../services/authService')
        await authService.logout()
      } catch (error) {
        console.warn('登出API调用失败:', error)
        // 即使API调用失败，也要清除本地状态
      } finally {
        // 清除本地状态
        this.token = ''
        this.userInfo = {}
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
      }
    }
  }
})
