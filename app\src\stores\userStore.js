import { defineStore } from 'pinia'
import { reactive } from 'vue'

// 安全的 JSON 解析函数
const safeJsonParse = (str, defaultValue = {}) => {
  try {
    return str ? JSON.parse(str) : defaultValue
  } catch (error) {
    console.warn('JSON解析失败:', error)
    return defaultValue
  }
}

export const useUserStore = defineStore('user', {
  state: () => ({
    token: localStorage.getItem('token') || '',
    userInfo: reactive(safeJsonParse(localStorage.getItem('userInfo')))
  }),
  getters: {
    isLogin: state => !!state.token
  },
  actions: {
    setToken(token) {
      this.token = token
      localStorage.setItem('token', token)
    },
    setUserInfo(info) {
      if (!info || typeof info !== 'object') {
        console.warn('无效的用户信息:', info)
        return
      }
      // 确保响应式更新
      Object.keys(this.userInfo).forEach(key => {
        delete this.userInfo[key]
      })
      Object.assign(this.userInfo, info)
      try {
        localStorage.setItem('userInfo', JSON.stringify(info))
      } catch (error) {
        console.error('保存用户信息到localStorage失败:', error)
      }
    },
    async logout() {
      try {
        // 调用登出API
        const { authService } = await import('../services/authService')
        await authService.logout()
      } catch (error) {
        console.warn('登出API调用失败:', error)
        // 即使API调用失败，也要清除本地状态
      } finally {
        // 清除本地状态
        this.token = ''
        // 正确清除响应式对象
        Object.keys(this.userInfo).forEach(key => {
          delete this.userInfo[key]
        })
        localStorage.removeItem('token')
        localStorage.removeItem('userInfo')
      }
    }
  }
})
