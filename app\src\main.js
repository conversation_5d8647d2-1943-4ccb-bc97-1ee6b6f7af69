import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import VueLazyload from 'vue-lazyload'
import ToastPlugin from './plugins/toast'
import { getDefaultAvatarUrl } from './config'
import './assets/styles/index.css'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(ToastPlugin)

app.use(VueLazyload, {
  preLoad: 1.3,
  error: getDefaultAvatarUrl(),
  loading: getDefaultAvatarUrl(),
  attempt: 1
})

// 应用初始化
app.mount('#app')
