import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import VueLazyload from 'vue-lazyload'
import ToastPlugin from './plugins/toast'
import { getDefaultAvatarUrl } from './config'
import './assets/styles/index.css'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(ToastPlugin)

// 获取默认头像URL
const defaultAvatarUrl = getDefaultAvatarUrl()

app.use(VueLazyload, {
  preLoad: 1.3,
  error: defaultAvatarUrl,
  loading: defaultAvatarUrl,
  attempt: 1
})

// 应用初始化
app.mount('#app')
