import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import VueLazyload from 'vue-lazyload'
import ToastPlugin from './plugins/toast'
import { userService } from './services/userService'
import { getDefaultAvatarUrl } from './config'
import './assets/styles/index.css'

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.use(router)
app.use(ToastPlugin)

// 获取默认头像URL
const defaultAvatarUrl = getDefaultAvatarUrl()

app.use(VueLazyload, {
  preLoad: 1.3,
  error: defaultAvatarUrl,
  loading: defaultAvatarUrl,
  attempt: 1
})

// 预加载省市数据
const preloadRegionData = async () => {
  try {
    console.log('正在预加载省市数据...')
    await userService.getRegions()
    console.log('省市数据预加载完成')
  } catch (error) {
    console.error('省市数据预加载失败:', error)
  }
}

// 应用初始化
const initApp = async () => {
  app.mount('#app')
  // 预加载数据
  preloadRegionData()
}

initApp()
