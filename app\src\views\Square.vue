<template>
  <page-layout title="广场">
    <div class="square-container">
      <!-- 筛选选项 -->
      <div class="filter-bar">
        <div
          class="filter-item"
          :class="{ active: activeFilter === 'latest' }"
          @click="setFilter('latest')"
        >
          最新
        </div>
        <div
          class="filter-item"
          :class="{ active: activeFilter === 'hot' }"
          @click="setFilter('hot')"
        >
          热门
        </div>
        <div
          class="filter-item"
          :class="{ active: activeFilter === 'follow' }"
          @click="setFilter('follow')"
        >
          关注
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading && posts.length === 0" class="loading-container">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="hasError && posts.length === 0" class="error-container">
        <div class="error-content">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="48"
            height="48"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <circle cx="12" cy="12" r="10"/>
            <line x1="12" y1="8" x2="12" y2="12"/>
            <line x1="12" y1="16" x2="12.01" y2="16"/>
          </svg>
          <h3>加载失败</h3>
          <p>网络连接异常，请检查网络后重试</p>
          <button class="retry-button" @click="refreshPosts">重新加载</button>
        </div>
      </div>

      <!-- 帖子列表 -->
      <div v-else class="post-list">
        <post-card
          v-for="post in filteredPosts"
          :key="post.id"
          :post="post"
          @like="likePost"
          @comment="commentPost"
          @share="sharePost"
          @toggle-privacy="togglePostPrivacy"
          @delete="deletePost"
        />

        <!-- 加载更多 -->
        <div v-if="hasMore && !loading" class="load-more-container">
          <button class="load-more-btn" @click="loadMore">
            加载更多
          </button>
        </div>

        <!-- 加载更多中 -->
        <div v-if="loading && posts.length > 0" class="loading-more">
          <div class="loading-spinner small"></div>
          <span>加载中...</span>
        </div>

        <!-- 没有更多内容 -->
        <div v-if="!hasMore && posts.length > 0" class="no-more">
          <span>没有更多内容了</span>
        </div>
      </div>

      <!-- 没有内容时显示 -->
      <empty-state
        v-if="!loading && !hasError && filteredPosts.length === 0"
        text="暂无内容"
        description="快来发布第一条动态吧"
      />
    </div>
  </page-layout>
</template>

<script setup>
  import { ref, computed, onMounted, onUnmounted, inject } from 'vue'
  import PageLayout from '../components/PageLayout.vue'
  import EmptyState from '../components/EmptyState.vue'
  import PostCard from '../components/PostCard.vue'
  import { useRouter } from 'vue-router'
  import { useUserStore } from '../stores/userStore.js'
  import { postService } from '../services/postService'

  // 帖子数据
  const posts = ref([])
  const loading = ref(true)
  const hasMore = ref(true)
  const currentPage = ref(1)
  const hasError = ref(false)
  const toast = inject('toast')
  const userStore = useUserStore()

  // 当前筛选条件
  const activeFilter = ref('latest')

  // 加载帖子列表
  const loadPosts = async (type = 'latest', page = 1, limit = 10) => {
    try {
      if (page === 1) {
        loading.value = true
        hasError.value = false
      }

      const response = await postService.getPosts({ type, page, limit })
      
      if (response && response.data) {
        // 转换数据格式以匹配前端组件期望的结构
        const newPosts = response.data.map(post => ({
          id: post.id,
          userId: post.userId,
          username: post.author?.nickname || post.author?.username || '用户',
          avatar: post.author?.avatar || '/default.png',
          content: post.content,
          images: post.images || [],
          location: post.location || '',
          createTime: new Date(post.createdAt || post.time),
          likeCount: post.stats?.likes || 0,
          commentCount: post.stats?.comments || 0,
          shareCount: post.stats?.shares || 0,
          isLiked: post.isLiked || false,
          isPrivate: post.privacy === 'private',
          isMine: post.userId === userStore.userInfo?.id // 判断是否为当前用户的帖子
        }))

        if (page === 1) {
          posts.value = newPosts
        } else {
          posts.value.push(...newPosts)
        }
        
        hasMore.value = response.pagination?.hasMore || false
        currentPage.value = page
        hasError.value = false
      }
    } catch (error) {
      console.error('获取帖子列表失败:', error)
      hasError.value = true
      if (page === 1) {
        toast?.error(error.message || '获取帖子列表失败')
      } else {
        toast?.error('加载更多失败')
      }
    } finally {
      loading.value = false
    }
  }

  // 筛选后的帖子
  const filteredPosts = computed(() => {
    // 帖子已经在服务端按类型排序，直接返回
    return posts.value
  })

  // 设置筛选条件
  const setFilter = async (filter) => {
    activeFilter.value = filter
    currentPage.value = 1
    await loadPosts(filter, 1)
  }

  // 加载更多
  const loadMore = async () => {
    if (!hasMore.value || loading.value) return
    
    const nextPage = currentPage.value + 1
    await loadPosts(activeFilter.value, nextPage)
  }

  // 下拉刷新
  const refreshPosts = async () => {
    currentPage.value = 1
    await loadPosts(activeFilter.value, 1)
  }



  // 点赞处理
  const likePost = async (postId) => {
    try {
      const post = posts.value.find(p => p.id === postId)
      if (!post) return

      const action = post.isLiked ? 'unlike' : 'like'
      await postService.likePost(postId, action)

      // 更新本地状态
      if (post.isLiked) {
        post.likeCount--
      } else {
        post.likeCount++
      }
      post.isLiked = !post.isLiked
    } catch (error) {
      console.error('点赞操作失败:', error)
      toast?.error(error.message || '点赞操作失败')
    }
  }

  const router = useRouter()

  // 评论处理
  const commentPost = postId => {
    // 评论帖子
    // 跳转到帖子详情页
    router.push(`/post/${postId}`)
  }

    // 分享处理
  const sharePost = async (postId) => {
    try {
      await postService.sharePost(postId)
      
      // 更新本地计数
      const post = posts.value.find(p => p.id === postId)
      if (post) {
        post.shareCount++
      }
      
      toast?.success('分享成功')
    } catch (error) {
      console.error('分享失败:', error)
      toast?.error(error.message || '分享失败')
    }
  }

  // 切换帖子隐私状态
  const togglePostPrivacy = async (postId) => {
    try {
      const post = posts.value.find(p => p.id === postId)
      if (!post || !post.isMine) return

      const newPrivacy = !post.isPrivate
      await postService.setPostPrivacy(postId, newPrivacy)

      // 更新本地状态
      post.isPrivate = newPrivacy
      
      if (newPrivacy) {
        toast?.success('帖子已设为私密')
      } else {
        toast?.success('帖子已设为公开')
      }
    } catch (error) {
      console.error('设置隐私状态失败:', error)
      toast?.error(error.message || '设置隐私状态失败')
    }
  }

  // 删除帖子
  const deletePost = async (postId) => {
    try {
      await postService.deletePost(postId)
      
      // 从本地列表中移除
      const index = posts.value.findIndex(p => p.id === postId)
      if (index !== -1) {
        posts.value.splice(index, 1)
      }
      
      toast?.success('帖子已删除')
    } catch (error) {
      console.error('删除帖子失败:', error)
      toast?.error(error.message || '删除帖子失败')
    }
  }

  // 页面挂载时
  onMounted(async () => {
    // 加载初始数据
    await loadPosts('latest', 1)
  })

  // 页面卸载时
  onUnmounted(() => {
    // 广场页面已卸载
  })
</script>

<style scoped>
  .square-container {
    width: 100%;
    padding: 15px 0 80px;
  }

  .filter-bar {
    display: flex;
    justify-content: space-around;
    background-color: #fff;
    padding: 12px 0;
    margin-bottom: 10px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .filter-item {
    font-size: 14px;
    color: #666;
    padding: 6px 16px;
    border-radius: 20px;
    transition: all 0.3s;
    cursor: pointer;
  }

  .filter-item.active {
    color: #fff;
    background-color: #ff6b6b;
    font-weight: 500;
  }

  .post-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-top: 15px;
  }

  /* 加载状态 */
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
  }

  .loading-container p {
    margin-top: 15px;
    color: #666;
    font-size: 14px;
  }

  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(255, 88, 100, 0.2);
    border-top-color: #ff5864;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  .loading-spinner.small {
    width: 20px;
    height: 20px;
    border-width: 2px;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* 加载更多 */
  .load-more-container {
    padding: 20px 0;
    text-align: center;
  }

  .load-more-btn {
    padding: 12px 24px;
    background-color: #f8f9fa;
    color: #666;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
  }

  .load-more-btn:hover {
    background-color: #e9ecef;
    color: #333;
  }

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 20px 0;
    color: #666;
    font-size: 14px;
  }

  .no-more {
    text-align: center;
    padding: 20px 0;
    color: #999;
    font-size: 14px;
  }

  /* 错误状态 */
  .error-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60px 20px;
  }

  .error-content {
    text-align: center;
    background-color: #fff;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }

  .error-content svg {
    color: #ff6b6b;
    margin-bottom: 20px;
  }

  .error-content h3 {
    font-size: 18px;
    font-weight: 500;
    margin: 0 0 10px 0;
    color: #333;
  }

  .error-content p {
    font-size: 14px;
    color: #666;
    margin: 0 0 20px 0;
  }

  .retry-button {
    background-color: #ff5864;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .retry-button:hover {
    background-color: #e54d5a;
  }
</style>
