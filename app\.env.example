# 前端环境变量配置示例
# 复制此文件为 .env.development、.env.production 等

# ===== 网络配置 =====
# API服务器主机地址
VITE_API_HOST=localhost
# API服务器端口
VITE_API_PORT=3000
# 前端开发服务器端口
VITE_DEV_PORT=5173

# API基础URL（生产环境使用）
VITE_API_BASE_URL=http://localhost:3000/api
# WebSocket URL
VITE_WS_URL=ws://localhost:3000/ws
# 图片服务器URL
VITE_IMAGE_BASE_URL=http://localhost:3000

# ===== 应用配置 =====
# 应用标题
VITE_APP_TITLE=社交匹配应用
# 默认头像路径
VITE_DEFAULT_AVATAR=/uploads/default-avatar.png

# ===== 网络超时配置 =====
# HTTP请求超时时间（毫秒）
VITE_REQUEST_TIMEOUT=60000
# WebSocket重连间隔（毫秒）
VITE_WS_RECONNECT_INTERVAL=3000
# WebSocket最大重连次数
VITE_WS_MAX_RECONNECT_ATTEMPTS=5

# ===== 分页配置 =====
# 默认分页大小
VITE_PAGE_SIZE=20

# ===== 文件上传配置 =====
# 最大文件大小（字节）
VITE_MAX_FILE_SIZE=10485760
# 最大头像文件大小（字节）
VITE_MAX_AVATAR_SIZE=5242880
# 允许的图片类型（逗号分隔）
VITE_ALLOWED_IMAGE_TYPES=image/jpeg,image/jpg,image/png,image/gif
# 允许的头像类型（逗号分隔）
VITE_ALLOWED_AVATAR_TYPES=image/jpeg,image/jpg,image/png
# 最大照片数量
VITE_MAX_PHOTOS_COUNT=6
# 最大帖子图片数量
VITE_MAX_POST_IMAGES=9
